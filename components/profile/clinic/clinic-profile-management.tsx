"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/trpc/react";
import { Link } from "@/lib/i18n/navigation";
import {
	Building2,
	CheckCircle,
	Clock,
	AlertCircle,
	Plus,
	Save,
} from "lucide-react";
import type { User } from "@/lib/types/profile";

interface ClinicProfileManagementProps {
	user: User;
	clinicProfile: any;
}

// Basic profile update schema
const profileUpdateSchema = z.object({
	name: z.string().min(2, "Clinic name must be at least 2 characters"),
	description: z.string().optional(),
	phone: z.string().min(10, "Phone number must be at least 10 characters"),
	email: z.string().email("Invalid email address"),
	website: z.string().url("Invalid website URL").optional().or(z.literal("")),
	address: z.string().min(5, "Address must be at least 5 characters"),
	city: z.string().min(2, "City must be at least 2 characters"),
	state: z.string().min(2, "State must be at least 2 characters"),
	postalCode: z.string().min(5, "Postal code must be at least 5 characters"),
});

type ProfileUpdateValues = z.infer<typeof profileUpdateSchema>;

export function ClinicProfileManagement({
	user,
	clinicProfile,
}: ClinicProfileManagementProps) {
	const t = useTranslations("profile");
	const { toast } = useToast();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// tRPC mutations
	const utils = api.useUtils();
	const createClinic = api.clinics.createAfterVerification.useMutation({
		onSuccess: () => {
			toast({
				title: "Clinic Profile Created",
				description: "Your clinic profile has been created successfully.",
			});
			utils.clinics.getMy.invalidate();
		},
		onError: (error) => {
			toast({
				title: "Error",
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const updateClinic = api.clinics.update.useMutation({
		onSuccess: () => {
			toast({
				title: "Profile Updated",
				description: "Your clinic profile has been updated successfully.",
			});
			utils.clinics.getMy.invalidate();
		},
		onError: (error) => {
			toast({
				title: "Error",
				description: error.message,
				variant: "destructive",
			});
		},
	});

	// Form setup
	const form = useForm<ProfileUpdateValues>({
		resolver: zodResolver(profileUpdateSchema),
		defaultValues: {
			name: clinicProfile?.name || "",
			description: clinicProfile?.description || "",
			phone: clinicProfile?.phone || "",
			email: clinicProfile?.email || user.email,
			website: clinicProfile?.website || "",
			address: clinicProfile?.address || "",
			city: clinicProfile?.city || "",
			state: clinicProfile?.state || "",
			postalCode: clinicProfile?.postalCode || "",
		},
	});

	// Form submission
	async function onSubmit(values: ProfileUpdateValues) {
		setIsSubmitting(true);

		try {
			if (clinicProfile) {
				// Update existing profile
				await updateClinic.mutateAsync({
					id: clinicProfile.id,
					...values,
				});
			} else {
				// Create new profile
				await createClinic.mutateAsync({
					...values,
					// Add default values for required fields
					operatingHours: {},
					emergencyHours: {},
					coordinates: null,
					serviceAreaRadius: 25,
					amenities: [],
					specializations: [],
					socialMedia: {},
					images: [],
				});
			}
		} catch (error) {
			// Error handling is done in mutation callbacks
		} finally {
			setIsSubmitting(false);
		}
	}

	// Verification status badge
	const getVerificationBadge = () => {
		if (!clinicProfile) return null;

		switch (clinicProfile.verificationStatus) {
			case "verified":
				return (
					<Badge variant="default" className="bg-green-100 text-green-800">
						<CheckCircle className="h-3 w-3 mr-1" />
						Verified
					</Badge>
				);
			case "pending":
				return (
					<Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
						<Clock className="h-3 w-3 mr-1" />
						Pending Review
					</Badge>
				);
			case "rejected":
				return (
					<Badge variant="destructive">
						<AlertCircle className="h-3 w-3 mr-1" />
						Rejected
					</Badge>
				);
			default:
				return null;
		}
	};

	return (
		<div className="space-y-6">
			{/* Status Card */}
			{clinicProfile && (
				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div className="flex items-center space-x-4">
								<Building2 className="h-8 w-8 text-teal-600" />
								<div>
									<h3 className="font-semibold">Clinic Status</h3>
									<p className="text-sm text-gray-600">
										Your clinic verification status
									</p>
								</div>
							</div>
							{getVerificationBadge()}
						</div>
						{clinicProfile.verificationStatus === "pending" && (
							<div className="mt-4 p-4 bg-yellow-50 rounded-lg">
								<p className="text-sm text-yellow-800">
									Your clinic profile is under review. You'll receive an email
									notification once the review is complete.
								</p>
							</div>
						)}
						{clinicProfile.verificationStatus === "rejected" && (
							<div className="mt-4 p-4 bg-red-50 rounded-lg">
								<p className="text-sm text-red-800">
									Your clinic profile was rejected.{" "}
									{clinicProfile.verificationNotes && (
										<span>Reason: {clinicProfile.verificationNotes}</span>
									)}
								</p>
							</div>
						)}
					</CardContent>
				</Card>
			)}

			{/* Profile Form */}
			<Card>
				<CardHeader>
					<CardTitle>
						{clinicProfile ? "Update Clinic Profile" : "Create Clinic Profile"}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							{/* Basic Information */}
							<div className="space-y-4">
								<h3 className="text-lg font-semibold">Basic Information</h3>
								
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Clinic Name</FormLabel>
											<FormControl>
												<Input {...field} placeholder="Your clinic name" />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="description"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Description</FormLabel>
											<FormControl>
												<Textarea
													{...field}
													placeholder="Describe your clinic and services..."
													rows={4}
												/>
											</FormControl>
											<FormDescription>
												This will be displayed on your public profile
											</FormDescription>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Contact Information */}
							<div className="space-y-4">
								<h3 className="text-lg font-semibold">Contact Information</h3>
								
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="phone"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Phone Number</FormLabel>
												<FormControl>
													<Input {...field} placeholder="+****************" />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="email"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Email</FormLabel>
												<FormControl>
													<Input {...field} type="email" />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<FormField
									control={form.control}
									name="website"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Website (Optional)</FormLabel>
											<FormControl>
												<Input
													{...field}
													placeholder="https://your-clinic-website.com"
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Location */}
							<div className="space-y-4">
								<h3 className="text-lg font-semibold">Location</h3>
								
								<FormField
									control={form.control}
									name="address"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Street Address</FormLabel>
											<FormControl>
												<Input {...field} placeholder="123 Main Street" />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
									<FormField
										control={form.control}
										name="city"
										render={({ field }) => (
											<FormItem>
												<FormLabel>City</FormLabel>
												<FormControl>
													<Input {...field} placeholder="City" />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="state"
										render={({ field }) => (
											<FormItem>
												<FormLabel>State/Province</FormLabel>
												<FormControl>
													<Input {...field} placeholder="State" />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="postalCode"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Postal Code</FormLabel>
												<FormControl>
													<Input {...field} placeholder="12345" />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							{/* Submit Button */}
							<div className="flex justify-end space-x-4">
								<Button
									type="submit"
									disabled={isSubmitting}
									className="min-w-[120px]"
								>
									{isSubmitting ? (
										<>
											<Clock className="h-4 w-4 mr-2 animate-spin" />
											Saving...
										</>
									) : (
										<>
											<Save className="h-4 w-4 mr-2" />
											{clinicProfile ? "Update Profile" : "Create Profile"}
										</>
									)}
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>

			{/* Next Steps */}
			{!clinicProfile && (
				<Card>
					<CardContent className="p-6">
						<h3 className="font-semibold mb-2">Next Steps</h3>
						<p className="text-sm text-gray-600 mb-4">
							After creating your profile, you can:
						</p>
						<ul className="text-sm text-gray-600 space-y-1">
							<li>• Add services your clinic offers</li>
							<li>• Set your operating hours and schedule</li>
							<li>• Upload photos of your clinic</li>
							<li>• Connect with local rescuers and pet owners</li>
						</ul>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
