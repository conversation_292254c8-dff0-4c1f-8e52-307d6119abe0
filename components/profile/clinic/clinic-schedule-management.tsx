"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/trpc/react";
import {
	Calendar,
	Clock,
	Save,
	AlertCircle,
	Copy,
	RotateCcw,
	Zap,
} from "lucide-react";
import type { User } from "@/lib/types/profile";

interface ClinicScheduleManagementProps {
	user: User;
	clinicProfile: any;
}

// Days of the week
const DAYS_OF_WEEK = [
	{ key: "monday", label: "Monday" },
	{ key: "tuesday", label: "Tuesday" },
	{ key: "wednesday", label: "Wednesday" },
	{ key: "thursday", label: "Thursday" },
	{ key: "friday", label: "Friday" },
	{ key: "saturday", label: "Saturday" },
	{ key: "sunday", label: "Sunday" },
];

// Schedule schema for a single day
const dayScheduleSchema = z.object({
	isOpen: z.boolean(),
	openTime: z.string().optional(),
	closeTime: z.string().optional(),
});

// Full schedule schema
const scheduleSchema = z.object({
	monday: dayScheduleSchema,
	tuesday: dayScheduleSchema,
	wednesday: dayScheduleSchema,
	thursday: dayScheduleSchema,
	friday: dayScheduleSchema,
	saturday: dayScheduleSchema,
	sunday: dayScheduleSchema,
	emergencyHours: z.object({
		available: z.boolean(),
		phone: z.string().optional(),
		notes: z.string().optional(),
	}),
});

type ScheduleValues = z.infer<typeof scheduleSchema>;

export function ClinicScheduleManagement({
	user,
	clinicProfile,
}: ClinicScheduleManagementProps) {
	const t = useTranslations("profile");
	const { toast } = useToast();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// tRPC mutations
	const utils = api.useUtils();
	const updateClinic = api.clinics.update.useMutation({
		onSuccess: () => {
			toast({
				title: "Schedule Updated",
				description:
					"Your clinic schedule has been updated successfully.",
			});
			utils.clinics.getMy.invalidate();
		},
		onError: (error) => {
			toast({
				title: "Error",
				description: error.message,
				variant: "destructive",
			});
		},
	});

	// Get default values from clinic profile
	const getDefaultSchedule = (): ScheduleValues => {
		const defaultDay = {
			isOpen: false,
			openTime: "09:00",
			closeTime: "17:00",
		};

		if (clinicProfile?.operatingHours) {
			const schedule = clinicProfile.operatingHours as any;
			return {
				monday: schedule.monday || defaultDay,
				tuesday: schedule.tuesday || defaultDay,
				wednesday: schedule.wednesday || defaultDay,
				thursday: schedule.thursday || defaultDay,
				friday: schedule.friday || defaultDay,
				saturday: schedule.saturday || defaultDay,
				sunday: schedule.sunday || defaultDay,
				emergencyHours: clinicProfile.emergencyHours || {
					available: false,
					phone: "",
					notes: "",
				},
			};
		}

		return {
			monday: defaultDay,
			tuesday: defaultDay,
			wednesday: defaultDay,
			thursday: defaultDay,
			friday: defaultDay,
			saturday: defaultDay,
			sunday: defaultDay,
			emergencyHours: {
				available: false,
				phone: "",
				notes: "",
			},
		};
	};

	// Form setup
	const form = useForm<ScheduleValues>({
		resolver: zodResolver(scheduleSchema),
		defaultValues: getDefaultSchedule(),
	});

	// Schedule templates
	const scheduleTemplates = {
		standard: {
			name: "Standard Business Hours",
			description: "Monday-Friday 9AM-5PM, Closed weekends",
			schedule: {
				monday: { isOpen: true, openTime: "09:00", closeTime: "17:00" },
				tuesday: {
					isOpen: true,
					openTime: "09:00",
					closeTime: "17:00",
				},
				wednesday: {
					isOpen: true,
					openTime: "09:00",
					closeTime: "17:00",
				},
				thursday: {
					isOpen: true,
					openTime: "09:00",
					closeTime: "17:00",
				},
				friday: { isOpen: true, openTime: "09:00", closeTime: "17:00" },
				saturday: {
					isOpen: false,
					openTime: "09:00",
					closeTime: "17:00",
				},
				sunday: {
					isOpen: false,
					openTime: "09:00",
					closeTime: "17:00",
				},
			},
		},
		extended: {
			name: "Extended Hours",
			description: "Monday-Saturday 8AM-7PM, Sunday 10AM-4PM",
			schedule: {
				monday: { isOpen: true, openTime: "08:00", closeTime: "19:00" },
				tuesday: {
					isOpen: true,
					openTime: "08:00",
					closeTime: "19:00",
				},
				wednesday: {
					isOpen: true,
					openTime: "08:00",
					closeTime: "19:00",
				},
				thursday: {
					isOpen: true,
					openTime: "08:00",
					closeTime: "19:00",
				},
				friday: { isOpen: true, openTime: "08:00", closeTime: "19:00" },
				saturday: {
					isOpen: true,
					openTime: "08:00",
					closeTime: "19:00",
				},
				sunday: { isOpen: true, openTime: "10:00", closeTime: "16:00" },
			},
		},
		emergency: {
			name: "24/7 Emergency Clinic",
			description: "Open 24 hours, 7 days a week",
			schedule: {
				monday: { isOpen: true, openTime: "00:00", closeTime: "23:59" },
				tuesday: {
					isOpen: true,
					openTime: "00:00",
					closeTime: "23:59",
				},
				wednesday: {
					isOpen: true,
					openTime: "00:00",
					closeTime: "23:59",
				},
				thursday: {
					isOpen: true,
					openTime: "00:00",
					closeTime: "23:59",
				},
				friday: { isOpen: true, openTime: "00:00", closeTime: "23:59" },
				saturday: {
					isOpen: true,
					openTime: "00:00",
					closeTime: "23:59",
				},
				sunday: { isOpen: true, openTime: "00:00", closeTime: "23:59" },
			},
		},
	};

	// Apply schedule template
	const applyTemplate = (templateKey: keyof typeof scheduleTemplates) => {
		const template = scheduleTemplates[templateKey];
		const currentValues = form.getValues();

		form.reset({
			...currentValues,
			...template.schedule,
		});

		toast({
			title: "Template Applied",
			description: `${template.name} schedule has been applied.`,
		});
	};

	// Copy schedule from one day to others
	const copyDaySchedule = (fromDay: string, toDays: string[]) => {
		const sourceSchedule = form.getValues(fromDay as any);

		toDays.forEach((day) => {
			form.setValue(`${day}.isOpen` as any, sourceSchedule.isOpen);
			form.setValue(`${day}.openTime` as any, sourceSchedule.openTime);
			form.setValue(`${day}.closeTime` as any, sourceSchedule.closeTime);
		});

		toast({
			title: "Schedule Copied",
			description: `${fromDay} schedule copied to ${toDays.length} day(s).`,
		});
	};

	// Form submission
	async function onSubmit(values: ScheduleValues) {
		if (!clinicProfile) {
			toast({
				title: "Error",
				description: "Please create your clinic profile first.",
				variant: "destructive",
			});
			return;
		}

		setIsSubmitting(true);

		try {
			const { emergencyHours, ...operatingHours } = values;

			await updateClinic.mutateAsync({
				id: clinicProfile.id,
				operatingHours,
				emergencyHours,
			});
		} catch (error) {
			// Error handling is done in mutation callbacks
		} finally {
			setIsSubmitting(false);
		}
	}

	// If no clinic profile exists, show setup prompt
	if (!clinicProfile) {
		return (
			<Card className="border-2 border-dashed border-gray-300">
				<CardContent className="flex flex-col items-center justify-center py-12">
					<Calendar className="h-12 w-12 text-gray-400 mb-4" />
					<h3 className="text-xl font-semibold text-gray-900 mb-2">
						Create Your Clinic Profile First
					</h3>
					<p className="text-gray-600 text-center mb-6 max-w-md">
						You need to create your clinic profile before you can
						manage your schedule and operating hours.
					</p>
					<Button asChild>
						<a href="/profile/clinic/profile">
							Complete Profile Setup
						</a>
					</Button>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className="space-y-6">
			<Form {...form}>
				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="space-y-6"
				>
					{/* Quick Templates */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center">
								<Zap className="h-5 w-5 mr-2" />
								Quick Schedule Templates
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
								{Object.entries(scheduleTemplates).map(
									([key, template]) => (
										<div
											key={key}
											className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
											onClick={() =>
												applyTemplate(
													key as keyof typeof scheduleTemplates
												)
											}
										>
											<h4 className="font-medium text-sm mb-1">
												{template.name}
											</h4>
											<p className="text-xs text-gray-600 mb-3">
												{template.description}
											</p>
											<Button
												type="button"
												variant="outline"
												size="sm"
												className="w-full"
												onClick={(e) => {
													e.stopPropagation();
													applyTemplate(
														key as keyof typeof scheduleTemplates
													);
												}}
											>
												<Copy className="h-3 w-3 mr-1" />
												Apply Template
											</Button>
										</div>
									)
								)}
							</div>
						</CardContent>
					</Card>

					{/* Operating Hours */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center">
								<Clock className="h-5 w-5 mr-2" />
								Operating Hours
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-6">
							{DAYS_OF_WEEK.map((day) => (
								<div key={day.key} className="space-y-4">
									<div className="flex items-center justify-between">
										<div className="flex items-center space-x-2">
											<h4 className="font-medium">
												{day.label}
											</h4>
											{form.watch(
												`${day.key}.isOpen` as any
											) && (
												<Button
													type="button"
													variant="ghost"
													size="sm"
													className="h-6 w-6 p-0"
													onClick={() => {
														const otherDays =
															DAYS_OF_WEEK.filter(
																(d) =>
																	d.key !==
																	day.key
															).map((d) => d.key);
														copyDaySchedule(
															day.key,
															otherDays
														);
													}}
													title={`Copy ${day.label} schedule to all other days`}
												>
													<Copy className="h-3 w-3" />
												</Button>
											)}
										</div>
										<FormField
											control={form.control}
											name={`${day.key}.isOpen` as any}
											render={({ field }) => (
												<FormItem className="flex items-center space-x-2">
													<FormControl>
														<Switch
															checked={
																field.value
															}
															onCheckedChange={
																field.onChange
															}
														/>
													</FormControl>
													<FormLabel className="text-sm">
														{field.value
															? "Open"
															: "Closed"}
													</FormLabel>
												</FormItem>
											)}
										/>
									</div>

									{form.watch(`${day.key}.isOpen` as any) && (
										<div className="grid grid-cols-2 gap-4 ml-4">
											<FormField
												control={form.control}
												name={
													`${day.key}.openTime` as any
												}
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Open Time
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																type="time"
																className="w-full"
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name={
													`${day.key}.closeTime` as any
												}
												render={({ field }) => (
													<FormItem>
														<FormLabel>
															Close Time
														</FormLabel>
														<FormControl>
															<Input
																{...field}
																type="time"
																className="w-full"
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
									)}
								</div>
							))}
						</CardContent>
					</Card>

					{/* Emergency Hours */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center">
								<AlertCircle className="h-5 w-5 mr-2" />
								Emergency Services
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<FormField
								control={form.control}
								name="emergencyHours.available"
								render={({ field }) => (
									<FormItem className="flex items-center justify-between">
										<div>
											<FormLabel>
												Emergency Services Available
											</FormLabel>
											<FormDescription>
												Do you provide emergency
												veterinary services?
											</FormDescription>
										</div>
										<FormControl>
											<Switch
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
									</FormItem>
								)}
							/>

							{form.watch("emergencyHours.available") && (
								<div className="space-y-4">
									<FormField
										control={form.control}
										name="emergencyHours.phone"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													Emergency Phone Number
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														placeholder="Emergency contact number"
													/>
												</FormControl>
												<FormDescription>
													Phone number for emergency
													services (can be different
													from main number)
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="emergencyHours.notes"
										render={({ field }) => (
											<FormItem>
												<FormLabel>
													Emergency Service Notes
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														placeholder="Additional information about emergency services"
													/>
												</FormControl>
												<FormDescription>
													Any additional information
													about your emergency
													services
												</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							)}
						</CardContent>
					</Card>

					{/* Submit Button */}
					<div className="flex justify-end">
						<Button
							type="submit"
							disabled={isSubmitting}
							className="min-w-[120px]"
						>
							{isSubmitting ? (
								<>
									<Clock className="h-4 w-4 mr-2 animate-spin" />
									Saving...
								</>
							) : (
								<>
									<Save className="h-4 w-4 mr-2" />
									Save Schedule
								</>
							)}
						</Button>
					</div>
				</form>
			</Form>
		</div>
	);
}
