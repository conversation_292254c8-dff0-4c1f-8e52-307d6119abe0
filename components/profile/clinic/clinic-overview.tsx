"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Link } from "@/lib/i18n/navigation";
import {
	Building2,
	MapPin,
	Phone,
	Globe,
	CheckCircle,
	Clock,
	AlertCircle,
	Plus,
	Edit,
	Calendar,
	Stethoscope,
} from "lucide-react";
import type { User } from "@/lib/types/profile";

interface ClinicOverviewProps {
	user: User;
	clinicProfile: any;
}

export function ClinicOverview({ user, clinicProfile }: ClinicOverviewProps) {
	const t = useTranslations("profile");

	// If no clinic profile exists, show setup prompt
	if (!clinicProfile) {
		return (
			<div className="space-y-6">
				{/* Setup Prompt */}
				<Card className="border-2 border-dashed border-gray-300">
					<CardContent className="flex flex-col items-center justify-center py-12">
						<Building2 className="h-12 w-12 text-gray-400 mb-4" />
						<h3 className="text-xl font-semibold text-gray-900 mb-2">
							Complete Your Clinic Profile
						</h3>
						<p className="text-gray-600 text-center mb-6 max-w-md">
							Set up your clinic profile to start helping cats find homes and
							connect with pet owners in your area.
						</p>
						<Button asChild>
							<Link href="/profile/clinic/profile">
								<Plus className="h-4 w-4 mr-2" />
								Complete Profile Setup
							</Link>
						</Button>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Verification status badge
	const getVerificationBadge = () => {
		switch (clinicProfile.verificationStatus) {
			case "verified":
				return (
					<Badge variant="default" className="bg-green-100 text-green-800">
						<CheckCircle className="h-3 w-3 mr-1" />
						Verified
					</Badge>
				);
			case "pending":
				return (
					<Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
						<Clock className="h-3 w-3 mr-1" />
						Pending Review
					</Badge>
				);
			case "rejected":
				return (
					<Badge variant="destructive">
						<AlertCircle className="h-3 w-3 mr-1" />
						Rejected
					</Badge>
				);
			default:
				return null;
		}
	};

	return (
		<div className="space-y-6">
			{/* Clinic Profile Summary */}
			<Card>
				<CardHeader>
					<div className="flex items-start justify-between">
						<div className="flex items-center space-x-4">
							<div className="w-16 h-16 bg-teal-100 rounded-lg flex items-center justify-center">
								<Building2 className="h-8 w-8 text-teal-600" />
							</div>
							<div>
								<CardTitle className="text-xl">{clinicProfile.name}</CardTitle>
								<div className="flex items-center space-x-2 mt-1">
									{getVerificationBadge()}
									{!clinicProfile.isActive && (
										<Badge variant="outline">Inactive</Badge>
									)}
								</div>
							</div>
						</div>
						<Button variant="outline" asChild>
							<Link href="/profile/clinic/profile">
								<Edit className="h-4 w-4 mr-2" />
								Edit Profile
							</Link>
						</Button>
					</div>
				</CardHeader>
				<CardContent className="space-y-4">
					{clinicProfile.description && (
						<p className="text-gray-600">{clinicProfile.description}</p>
					)}
					
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="flex items-center space-x-2 text-sm text-gray-600">
							<MapPin className="h-4 w-4" />
							<span>
								{clinicProfile.address}, {clinicProfile.city},{" "}
								{clinicProfile.state}
							</span>
						</div>
						<div className="flex items-center space-x-2 text-sm text-gray-600">
							<Phone className="h-4 w-4" />
							<span>{clinicProfile.phone}</span>
						</div>
						{clinicProfile.website && (
							<div className="flex items-center space-x-2 text-sm text-gray-600">
								<Globe className="h-4 w-4" />
								<a
									href={clinicProfile.website}
									target="_blank"
									rel="noopener noreferrer"
									className="text-teal-600 hover:underline"
								>
									{clinicProfile.website}
								</a>
							</div>
						)}
					</div>
				</CardContent>
			</Card>

			{/* Quick Stats */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-gray-600">Services</p>
								<p className="text-2xl font-bold text-gray-900">
									{clinicProfile.services?.length || 0}
								</p>
							</div>
							<Stethoscope className="h-8 w-8 text-teal-600" />
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-gray-600">Rating</p>
								<p className="text-2xl font-bold text-gray-900">
									{clinicProfile.ratingAverage?.toFixed(1) || "N/A"}
								</p>
							</div>
							<CheckCircle className="h-8 w-8 text-teal-600" />
						</div>
					</CardContent>
				</Card>

				<Card>
					<CardContent className="p-6">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-gray-600">Reviews</p>
								<p className="text-2xl font-bold text-gray-900">
									{clinicProfile.reviewCount || 0}
								</p>
							</div>
							<Building2 className="h-8 w-8 text-teal-600" />
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Quick Actions */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				<Card>
					<CardHeader>
						<CardTitle className="text-lg">Manage Services</CardTitle>
					</CardHeader>
					<CardContent>
						<p className="text-gray-600 mb-4">
							Add and manage the services your clinic offers to help pet owners
							find you.
						</p>
						<Button asChild className="w-full">
							<Link href="/profile/services">
								<Stethoscope className="h-4 w-4 mr-2" />
								Manage Services
							</Link>
						</Button>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle className="text-lg">Schedule & Hours</CardTitle>
					</CardHeader>
					<CardContent>
						<p className="text-gray-600 mb-4">
							Set your operating hours and manage your clinic's availability.
						</p>
						<Button asChild variant="outline" className="w-full">
							<Link href="/profile/clinic/schedule">
								<Calendar className="h-4 w-4 mr-2" />
								Manage Schedule
							</Link>
						</Button>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
