"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
	Edit,
	Trash2,
	Plus,
	CheckC<PERSON>cle,
	Clock,
	Stethoscope,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { api } from "@/lib/trpc/react";
import { Link } from "@/lib/i18n/navigation";
import type { User } from "@/lib/types/profile";

interface ClinicServicesProps {
	user: User;
	clinicProfile: any;
}

export function ClinicServices({ user, clinicProfile }: ClinicServicesProps) {
	const t = useTranslations("profile");
	const { toast } = useToast();
	const [dialogOpen, setDialogOpen] = useState(false);
	const [serviceToDelete, setServiceToDelete] = useState<any>(null);

	// tRPC queries and mutations
	const utils = api.useUtils();
	const { data: services = [], isLoading } =
		api.clinicServices.getMy.useQuery(undefined, {
			enabled: !!clinicProfile,
		});

	const deleteServiceMutation = api.clinicServices.delete.useMutation({
		onSuccess: () => {
			toast({
				title: "Service deleted",
				description: "The service has been removed from your profile.",
			});
			utils.clinicServices.getMy.invalidate();
			setDialogOpen(false);
		},
		onError: (error) => {
			toast({
				title: "Error",
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const toggleAvailabilityMutation =
		api.clinicServices.toggleAvailability.useMutation({
			onSuccess: (_, variables) => {
				toast({
					title: variables.isAvailable
						? "Service marked as available"
						: "Service marked as unavailable",
					description: "Service availability has been updated.",
				});
				utils.clinicServices.getMy.invalidate();
			},
			onError: (error) => {
				toast({
					title: "Error",
					description: error.message,
					variant: "destructive",
				});
			},
		});

	const deleteService = (id: number) => {
		deleteServiceMutation.mutate({ id });
	};

	const openDeleteDialog = (service: any) => {
		setServiceToDelete(service);
		setDialogOpen(true);
	};

	const toggleServiceAvailability = (
		id: number,
		currentAvailability: boolean
	) => {
		toggleAvailabilityMutation.mutate({
			id,
			isAvailable: !currentAvailability,
		});
	};

	// Show loading state
	if (isLoading) {
		return (
			<div className="text-center py-12">
				<Stethoscope className="h-12 w-12 text-gray-400 mx-auto mb-4 animate-pulse" />
				<h3 className="text-xl font-medium mb-2">
					Loading services...
				</h3>
				<p className="text-muted-foreground">
					Please wait while we load your clinic services.
				</p>
			</div>
		);
	}

	// Show empty state if no clinic profile
	if (!clinicProfile) {
		return (
			<div className="text-center py-12">
				<Stethoscope className="h-12 w-12 text-gray-400 mx-auto mb-4" />
				<h3 className="text-xl font-medium mb-2">
					Create Your Clinic Profile First
				</h3>
				<p className="text-muted-foreground mb-6">
					You need to create your clinic profile before you can manage
					services.
				</p>
				<Button asChild>
					<Link href="/profile/clinic/profile">
						Complete Profile Setup
					</Link>
				</Button>
			</div>
		);
	}

	// Show empty services state
	if (services.length === 0) {
		return (
			<div className="text-center py-12">
				<Stethoscope className="h-12 w-12 text-gray-400 mx-auto mb-4" />
				<h3 className="text-xl font-medium mb-2">
					No services listed yet
				</h3>
				<p className="text-muted-foreground mb-6">
					Add services that your clinic offers to help cat owners and
					rescuers.
				</p>
				<Button asChild>
					<Link href="/profile/services/new">
						<Plus className="h-4 w-4 mr-2" />
						Add Service
					</Link>
				</Button>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<p className="text-muted-foreground">
					Showing {services.length} services
				</p>
				<Button asChild>
					<Link href="/profile/services/new">
						<Plus className="h-4 w-4 mr-2" />
						Add Service
					</Link>
				</Button>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{services.map((service) => (
					<Card
						key={service.id}
						className={`${!service.isAvailable ? "opacity-75" : ""}`}
					>
						<CardContent className="p-6">
							<div className="flex justify-between items-start mb-2">
								<h3 className="text-xl font-bold">
									{service.name}
								</h3>
								<div className="flex items-center gap-2">
									<Switch
										checked={service.isAvailable}
										onCheckedChange={() =>
											toggleServiceAvailability(
												service.id,
												service.isAvailable
											)
										}
										aria-label="Toggle availability"
										disabled={
											toggleAvailabilityMutation.isPending
										}
									/>
									<span className="text-sm text-muted-foreground">
										{service.isAvailable
											? "Available"
											: "Unavailable"}
									</span>
								</div>
							</div>

							<div className="flex flex-wrap gap-2 mb-3">
								{service.category && (
									<Badge variant="secondary">
										{service.category
											.replace(/_/g, " ")
											.replace(/\b\w/g, (l) =>
												l.toUpperCase()
											)}
									</Badge>
								)}
								{service.duration && (
									<Badge variant="outline">
										<Clock className="h-3.5 w-3.5 mr-1" />
										{service.duration} min
									</Badge>
								)}
								{service.price && (
									<Badge variant="outline">
										${service.price}
									</Badge>
								)}
							</div>

							<p className="text-muted-foreground mb-4">
								{service.description}
							</p>
						</CardContent>
						<CardFooter className="px-6 py-4 pt-0 flex gap-2">
							<Button
								asChild
								variant="outline"
								className="flex-1"
							>
								<Link
									href={`/profile/services/${service.id}/edit`}
								>
									<Edit className="h-4 w-4 mr-2" />
									Edit
								</Link>
							</Button>
							<Button
								variant="outline"
								size="icon"
								className="text-red-500 hover:text-red-600"
								onClick={() => openDeleteDialog(service)}
								disabled={deleteServiceMutation.isPending}
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</CardFooter>
					</Card>
				))}
			</div>

			<Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Delete Service</DialogTitle>
						<DialogDescription>
							Are you sure you want to delete the{" "}
							{serviceToDelete?.name} service? This action cannot
							be undone.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button
							variant="destructive"
							onClick={() => deleteService(serviceToDelete?.id)}
							disabled={deleteServiceMutation.isPending}
						>
							{deleteServiceMutation.isPending
								? "Deleting..."
								: "Delete Service"}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
