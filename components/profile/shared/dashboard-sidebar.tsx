"use client";

import type React from "react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { Loader2, X } from "lucide-react";
import { Link } from "@/lib/i18n/navigation";
import type { User, SidebarItem, QuickStat } from "@/lib/types/profile";

// Alias for backward compatibility
type NavigationItem = SidebarItem;

interface DashboardSidebarProps {
	user: User;
	navigationItems: NavigationItem[];
	quickStats: QuickStat[];
	activeTab: string;
	onTabChange: (tab: string) => void;
	isOpen: boolean;
	onClose: () => void;
	isRTL: boolean;
	quickActionButton?: React.ReactNode;
	colorClasses: Record<string, string>;
	className?: string;
	useRouting?: boolean; // New prop to enable route-based navigation
	isLoadingStats: boolean;
}

export function DashboardSidebar({
	user,
	navigationItems,
	quickStats,
	activeTab,
	onTabChange,
	isOpen: _isOpen,
	onClose,
	isRTL,
	quickActionButton,
	colorClasses,
	className,
	useRouting = false,
	isLoadingStats,
}: DashboardSidebarProps) {
	const t = useTranslations("profile");

	return (
		<div className={cn("flex flex-col", className)}>
			{/* Sidebar Header */}
			<div className="p-6 border-b border-gray-200 flex-shrink-0">
				<div className="flex items-center justify-between">
					<div
						className={cn(
							"flex items-center",
							isRTL ? "space-x-reverse space-x-4" : "space-x-4"
						)}
					>
						<div className="relative w-12 h-12 rounded-full overflow-hidden bg-gray-100">
							<Image
								src={user.image || "/avatar-placeholder.png"}
								alt={user.name}
								fill
								className="object-cover"
								onError={(e) => {
									e.currentTarget.src =
										"/avatar-placeholder.png";
								}}
							/>
						</div>
						<div>
							<h2 className="text-lg font-display font-bold text-gray-900">
								{user.name}
							</h2>
							<p className="text-sm text-gray-600">
								{t("memberSince")}{" "}
								{new Date(user.createdAt).getFullYear()}
							</p>
						</div>
					</div>
					<button
						onClick={onClose}
						className="lg:hidden p-2 rounded-md hover:bg-gray-100"
					>
						<X className="w-5 h-5" />
					</button>
				</div>
			</div>

			{/* Quick Stats */}
			<div className="p-4 lg:p-6 border-b border-gray-200 flex-shrink-0">
				<div className="grid grid-cols-2 gap-3 lg:gap-4">
					{quickStats.map((stat, index) => (
						<div
							key={index}
							className={cn(
								"text-center p-3 rounded-xl",
								colorClasses[stat.color],
								index === quickStats.length - 1
									? "col-span-2"
									: ""
							)}
						>
							<div className="text-lg lg:text-xl font-bold flex justify-center">
								{isLoadingStats ? (
									<Loader2
										className={cn(
											"h-6 w-6 animate-spin",
											colorClasses[stat.color]
										)}
									/>
								) : (
									stat.value
								)}
							</div>
							<div className="text-xs text-gray-600">
								{t(stat.labelKey)}
							</div>
						</div>
					))}
				</div>
			</div>

			{/* Navigation */}
			<nav className="flex-1 p-4 overflow-y-auto">
				<ul className="space-y-2">
					{navigationItems.map((item) => {
						const Icon = item.icon;
						const isActive = activeTab === item.id;
						const linkClassName = cn(
							"w-full cursor-pointer flex items-center justify-between p-4 rounded-xl transition-colors text-left min-h-[44px]",
							isActive
								? cn(
										"bg-teal-50 text-teal-700 border-teal-500",
										isRTL ? "border-r-4" : "border-l-4"
									)
								: "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
						);

						const content = (
							<div
								className={cn(
									"flex items-center",
									isRTL
										? "space-x-reverse space-x-3"
										: "space-x-3"
								)}
							>
								<Icon className="w-5 h-5" />
								<div>
									<div className="font-medium">
										{t(item.labelKey)}
									</div>
									<div className="text-xs text-gray-500">
										{t(item.descriptionKey) || ""}
									</div>
								</div>
							</div>
						);

						// Determine the correct href based on the item
						const getHref = (itemId: string) => {
							// Handle clinic-specific routes
							if (itemId === "clinic-overview")
								return "/profile/clinic";
							if (itemId === "clinic-profile")
								return "/profile/clinic/profile";
							if (itemId === "clinic-schedule")
								return "/profile/clinic/schedule";

							// All other profile navigation items should go to profile sub-routes
							return `/profile/${itemId}`;
						};

						return (
							<li key={item.id}>
								{useRouting ? (
									<Link
										href={getHref(item.id)}
										className={linkClassName}
										onClick={() => onClose()}
									>
										{content}
									</Link>
								) : (
									<button
										onClick={() => onTabChange(item.id)}
										className={linkClassName}
									>
										{content}
									</button>
								)}
							</li>
						);
					})}
				</ul>
			</nav>

			{/* Quick Actions */}
			{quickActionButton && (
				<div className="p-4 border-t border-gray-200 flex-shrink-0 mt-auto">
					{quickActionButton}
				</div>
			)}
		</div>
	);
}
