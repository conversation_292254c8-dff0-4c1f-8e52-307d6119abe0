import * as React from "react";

interface AdminClinicNotificationProps {
	clinicName: string;
	ownerName: string;
	ownerEmail: string;
	adminDashboardUrl: string;
	clinicId: number;
	registrationDate: string;
}

export const AdminClinicNotification: React.FC<
	Readonly<AdminClinicNotificationProps>
> = ({ 
	clinicName, 
	ownerName, 
	ownerEmail, 
	adminDashboardUrl, 
	clinicId,
	registrationDate 
}) => {
	return (
		<div style={{ 
			fontFamily: 'Arial, sans-serif', 
			maxWidth: '600px', 
			margin: '0 auto' 
		}}>
			{/* Header */}
			<div style={{ 
				backgroundColor: '#dc2626', 
				color: 'white', 
				padding: '20px', 
				textAlign: 'center' 
			}}>
				<h1 style={{ margin: '0', fontSize: '24px' }}>
					🚨 Admin Alert - New Clinic Registration
				</h1>
			</div>

			{/* Content */}
			<div style={{ padding: '30px', backgroundColor: '#ffffff' }}>
				<h2 style={{ 
					color: '#dc2626', 
					fontSize: '20px', 
					marginBottom: '20px' 
				}}>
					New Clinic Registration Requires Review
				</h2>

				<p style={{ 
					fontSize: '16px', 
					lineHeight: '1.6', 
					marginBottom: '20px' 
				}}>
					A new veterinary clinic has registered on the platform and is awaiting verification.
				</p>

				{/* Clinic Details */}
				<div style={{ 
					backgroundColor: '#f8fafc', 
					border: '1px solid #e2e8f0', 
					borderRadius: '8px', 
					padding: '20px', 
					marginBottom: '20px' 
				}}>
					<h3 style={{ 
						color: '#334155', 
						fontSize: '18px', 
						marginBottom: '15px' 
					}}>
						Clinic Details
					</h3>
					
					<div style={{ marginBottom: '10px' }}>
						<strong>Clinic Name:</strong> {clinicName}
					</div>
					
					<div style={{ marginBottom: '10px' }}>
						<strong>Owner Name:</strong> {ownerName}
					</div>
					
					<div style={{ marginBottom: '10px' }}>
						<strong>Owner Email:</strong> {ownerEmail}
					</div>
					
					<div style={{ marginBottom: '10px' }}>
						<strong>Registration Date:</strong> {registrationDate}
					</div>
					
					<div style={{ marginBottom: '10px' }}>
						<strong>Clinic ID:</strong> #{clinicId}
					</div>
				</div>

				{/* Action Required */}
				<div style={{ 
					backgroundColor: '#fef3c7', 
					border: '1px solid #f59e0b', 
					borderRadius: '8px', 
					padding: '15px', 
					marginBottom: '20px' 
				}}>
					<h4 style={{ 
						color: '#92400e', 
						fontSize: '16px', 
						marginBottom: '10px' 
					}}>
						⚠️ Action Required
					</h4>
					<p style={{ 
						color: '#92400e', 
						margin: '0', 
						lineHeight: '1.5' 
					}}>
						Please review the clinic registration and verify the provided information. 
						The clinic owner is waiting for approval to start using the platform.
					</p>
				</div>

				{/* Review Steps */}
				<div style={{ 
					backgroundColor: '#ecfdf5', 
					padding: '20px', 
					borderRadius: '8px', 
					marginBottom: '20px' 
				}}>
					<h3 style={{ 
						color: '#334155', 
						fontSize: '18px', 
						marginBottom: '15px' 
					}}>
						Review Process
					</h3>
					<ol style={{ 
						paddingLeft: '20px',
						margin: '0' 
					}}>
						<li style={{ marginBottom: '8px', lineHeight: '1.5' }}>
							Access the admin dashboard using the button below
						</li>
						<li style={{ marginBottom: '8px', lineHeight: '1.5' }}>
							Review the clinic's profile information and documentation
						</li>
						<li style={{ marginBottom: '8px', lineHeight: '1.5' }}>
							Verify the clinic's credentials and contact information
						</li>
						<li style={{ marginBottom: '8px', lineHeight: '1.5' }}>
							Approve or reject the application with appropriate feedback
						</li>
					</ol>
				</div>

				{/* Action Button */}
				<div style={{ 
					textAlign: 'center', 
					marginBottom: '30px' 
				}}>
					<a 
						href={adminDashboardUrl}
						style={{
							display: 'inline-block',
							backgroundColor: '#dc2626',
							color: 'white',
							padding: '15px 30px',
							textDecoration: 'none',
							borderRadius: '6px',
							fontWeight: 'bold',
							fontSize: '16px'
						}}
					>
						Review Clinic Registration
					</a>
				</div>

				{/* Priority Notice */}
				<div style={{ 
					backgroundColor: '#fef2f2', 
					border: '1px solid #fca5a5', 
					borderRadius: '8px', 
					padding: '15px', 
					marginBottom: '20px' 
				}}>
					<p style={{ 
						color: '#dc2626', 
						margin: '0', 
						fontWeight: 'bold',
						textAlign: 'center' 
					}}>
						🕒 Please review within 2-3 business days to maintain good user experience
					</p>
				</div>

				<p style={{ 
					fontSize: '14px', 
					color: '#64748b',
					lineHeight: '1.5',
					marginBottom: '20px' 
				}}>
					This is an automated notification. If you have any questions about the review process, 
					please contact the development team.
				</p>

				<p style={{ 
					fontSize: '16px', 
					fontWeight: 'bold', 
					color: '#dc2626' 
				}}>
					Paws & Whiskers Admin System
				</p>
			</div>

			{/* Footer */}
			<div style={{ 
				backgroundColor: '#f1f5f9', 
				padding: '20px', 
				textAlign: 'center', 
				fontSize: '14px', 
				color: '#64748b' 
			}}>
				<p style={{ margin: '0' }}>
					© 2025 Paws & Whiskers Admin System. Internal Use Only.
				</p>
			</div>
		</div>
	);
};
