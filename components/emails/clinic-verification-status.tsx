import * as React from "react";

interface ClinicVerificationStatusProps {
	clinicName: string;
	ownerName: string;
	status: "verified" | "rejected";
	notes?: string;
	clinicUrl?: string;
	locale?: string;
}

export const ClinicVerificationStatus: React.FC<
	Readonly<ClinicVerificationStatusProps>
> = ({ clinicName, ownerName, status, notes, clinicUrl, locale = "en" }) => {
	const isRTL = locale === "ar";
	const isVerified = status === "verified";
	
	const content = {
		en: {
			subject: isVerified ? "Clinic Verification Approved!" : "Clinic Verification Update",
			greeting: `Dear ${ownerName},`,
			verifiedTitle: "Congratulations! Your clinic has been verified! 🎉",
			rejectedTitle: "Clinic Verification Update",
			verifiedMessage: `We're excited to inform you that "${clinicName}" has been successfully verified and is now live on our platform!`,
			rejectedMessage: `We have completed the review of your clinic registration for "${clinicName}". Unfortunately, we need some additional information or corrections before we can approve your clinic.`,
			verifiedBenefits: "What this means for you:",
			verifiedSteps: [
				"Your clinic is now visible to users searching for veterinary services",
				"You can start receiving inquiries and bookings from pet owners",
				"You have access to your clinic dashboard to manage your profile and services",
				"You can respond to reviews and engage with the community"
			],
			rejectedNextSteps: "Next steps:",
			rejectedSteps: [
				"Review the feedback provided below",
				"Make the necessary corrections to your clinic information",
				"Contact our support team if you need assistance",
				"Resubmit your application once the issues are addressed"
			],
			notesLabel: "Feedback from our review team:",
			viewClinic: "View Your Clinic Profile",
			contactSupport: "Contact Support",
			verifiedThanks: "Thank you for joining our community of trusted veterinary professionals!",
			rejectedThanks: "We appreciate your patience and look forward to welcoming you to our platform soon.",
			team: "The Paws & Whiskers Team"
		},
		fr: {
			subject: isVerified ? "Vérification de clinique approuvée !" : "Mise à jour de la vérification de clinique",
			greeting: `Cher/Chère ${ownerName},`,
			verifiedTitle: "Félicitations ! Votre clinique a été vérifiée ! 🎉",
			rejectedTitle: "Mise à jour de la vérification de clinique",
			verifiedMessage: `Nous sommes ravis de vous informer que "${clinicName}" a été vérifiée avec succès et est maintenant en ligne sur notre plateforme !`,
			rejectedMessage: `Nous avons terminé l'examen de votre inscription de clinique pour "${clinicName}". Malheureusement, nous avons besoin d'informations supplémentaires ou de corrections avant de pouvoir approuver votre clinique.`,
			verifiedBenefits: "Ce que cela signifie pour vous :",
			verifiedSteps: [
				"Votre clinique est maintenant visible aux utilisateurs recherchant des services vétérinaires",
				"Vous pouvez commencer à recevoir des demandes et des réservations de propriétaires d'animaux",
				"Vous avez accès à votre tableau de bord de clinique pour gérer votre profil et vos services",
				"Vous pouvez répondre aux avis et interagir avec la communauté"
			],
			rejectedNextSteps: "Prochaines étapes :",
			rejectedSteps: [
				"Examinez les commentaires fournis ci-dessous",
				"Apportez les corrections nécessaires aux informations de votre clinique",
				"Contactez notre équipe de support si vous avez besoin d'aide",
				"Resoumettez votre candidature une fois les problèmes résolus"
			],
			notesLabel: "Commentaires de notre équipe d'examen :",
			viewClinic: "Voir le profil de votre clinique",
			contactSupport: "Contacter le support",
			verifiedThanks: "Merci de rejoindre notre communauté de professionnels vétérinaires de confiance !",
			rejectedThanks: "Nous apprécions votre patience et avons hâte de vous accueillir sur notre plateforme bientôt.",
			team: "L'équipe Paws & Whiskers"
		},
		ar: {
			subject: isVerified ? "تمت الموافقة على تحقق العيادة!" : "تحديث تحقق العيادة",
			greeting: `عزيزي/عزيزتي ${ownerName}،`,
			verifiedTitle: "تهانينا! تم التحقق من عيادتك! 🎉",
			rejectedTitle: "تحديث تحقق العيادة",
			verifiedMessage: `يسعدنا أن نخبرك أن "${clinicName}" تم التحقق منها بنجاح وهي الآن متاحة على منصتنا!`,
			rejectedMessage: `لقد أكملنا مراجعة تسجيل عيادتك لـ "${clinicName}". للأسف، نحتاج إلى بعض المعلومات الإضافية أو التصحيحات قبل أن نتمكن من الموافقة على عيادتك.`,
			verifiedBenefits: "ما يعنيه هذا بالنسبة لك:",
			verifiedSteps: [
				"عيادتك مرئية الآن للمستخدمين الذين يبحثون عن الخدمات البيطرية",
				"يمكنك البدء في تلقي الاستفسارات والحجوزات من أصحاب الحيوانات الأليفة",
				"لديك إمكانية الوصول إلى لوحة تحكم عيادتك لإدارة ملفك الشخصي وخدماتك",
				"يمكنك الرد على المراجعات والتفاعل مع المجتمع"
			],
			rejectedNextSteps: "الخطوات التالية:",
			rejectedSteps: [
				"راجع التعليقات المقدمة أدناه",
				"قم بإجراء التصحيحات اللازمة لمعلومات عيادتك",
				"اتصل بفريق الدعم لدينا إذا كنت بحاجة إلى مساعدة",
				"أعد تقديم طلبك بمجرد معالجة المشاكل"
			],
			notesLabel: "تعليقات من فريق المراجعة لدينا:",
			viewClinic: "عرض ملف عيادتك الشخصي",
			contactSupport: "اتصل بالدعم",
			verifiedThanks: "شكراً لانضمامك إلى مجتمعنا من المهنيين البيطريين الموثوقين!",
			rejectedThanks: "نقدر صبرك ونتطلع إلى الترحيب بك على منصتنا قريباً.",
			team: "فريق Paws & Whiskers"
		}
	};

	const t = content[locale as keyof typeof content] || content.en;

	return (
		<div style={{ 
			fontFamily: 'Arial, sans-serif', 
			maxWidth: '600px', 
			margin: '0 auto',
			direction: isRTL ? 'rtl' : 'ltr'
		}}>
			{/* Header */}
			<div style={{ 
				backgroundColor: isVerified ? '#10b981' : '#f59e0b', 
				color: 'white', 
				padding: '20px', 
				textAlign: 'center' 
			}}>
				<h1 style={{ margin: '0', fontSize: '24px' }}>
					🐾 Paws & Whiskers
				</h1>
			</div>

			{/* Content */}
			<div style={{ padding: '30px', backgroundColor: '#ffffff' }}>
				<p style={{ fontSize: '16px', marginBottom: '10px' }}>
					{t.greeting}
				</p>

				<h2 style={{ 
					color: isVerified ? '#10b981' : '#f59e0b', 
					fontSize: '20px', 
					marginBottom: '20px' 
				}}>
					{isVerified ? t.verifiedTitle : t.rejectedTitle}
				</h2>

				<p style={{ 
					fontSize: '16px', 
					lineHeight: '1.6', 
					marginBottom: '20px' 
				}}>
					{isVerified ? t.verifiedMessage : t.rejectedMessage}
				</p>

				{/* Benefits/Next Steps */}
				<div style={{ 
					backgroundColor: isVerified ? '#ecfdf5' : '#fef3c7', 
					padding: '20px', 
					borderRadius: '8px', 
					marginBottom: '20px' 
				}}>
					<h3 style={{ 
						color: '#334155', 
						fontSize: '18px', 
						marginBottom: '15px' 
					}}>
						{isVerified ? t.verifiedBenefits : t.rejectedNextSteps}
					</h3>
					<ul style={{ 
						paddingLeft: isRTL ? '0' : '20px',
						paddingRight: isRTL ? '20px' : '0',
						margin: '0' 
					}}>
						{(isVerified ? t.verifiedSteps : t.rejectedSteps).map((step, index) => (
							<li key={index} style={{ 
								marginBottom: '8px', 
								lineHeight: '1.5' 
							}}>
								{step}
							</li>
						))}
					</ul>
				</div>

				{/* Notes section for rejected applications */}
				{!isVerified && notes && (
					<div style={{ 
						backgroundColor: '#fef2f2', 
						border: '1px solid #fca5a5', 
						borderRadius: '8px', 
						padding: '15px', 
						marginBottom: '20px' 
					}}>
						<h4 style={{ 
							color: '#dc2626', 
							fontSize: '16px', 
							marginBottom: '10px' 
						}}>
							{t.notesLabel}
						</h4>
						<p style={{ 
							color: '#7f1d1d', 
							margin: '0', 
							lineHeight: '1.5' 
						}}>
							{notes}
						</p>
					</div>
				)}

				{/* Action buttons */}
				<div style={{ 
					textAlign: 'center', 
					marginBottom: '30px' 
				}}>
					{isVerified && clinicUrl && (
						<a 
							href={clinicUrl}
							style={{
								display: 'inline-block',
								backgroundColor: '#10b981',
								color: 'white',
								padding: '12px 24px',
								textDecoration: 'none',
								borderRadius: '6px',
								fontWeight: 'bold',
								marginRight: isRTL ? '0' : '10px',
								marginLeft: isRTL ? '10px' : '0'
							}}
						>
							{t.viewClinic}
						</a>
					)}
					<a 
						href="mailto:<EMAIL>"
						style={{
							display: 'inline-block',
							backgroundColor: '#6b7280',
							color: 'white',
							padding: '12px 24px',
							textDecoration: 'none',
							borderRadius: '6px',
							fontWeight: 'bold'
						}}
					>
						{t.contactSupport}
					</a>
				</div>

				<p style={{ 
					fontSize: '16px', 
					lineHeight: '1.6', 
					marginBottom: '20px' 
				}}>
					{isVerified ? t.verifiedThanks : t.rejectedThanks}
				</p>

				<p style={{ 
					fontSize: '16px', 
					fontWeight: 'bold', 
					color: isVerified ? '#10b981' : '#f59e0b' 
				}}>
					{t.team}
				</p>
			</div>

			{/* Footer */}
			<div style={{ 
				backgroundColor: '#f1f5f9', 
				padding: '20px', 
				textAlign: 'center', 
				fontSize: '14px', 
				color: '#64748b' 
			}}>
				<p style={{ margin: '0' }}>
					© 2025 Paws & Whiskers. All rights reserved.
				</p>
			</div>
		</div>
	);
};
