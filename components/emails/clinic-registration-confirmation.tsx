import * as React from "react";

interface ClinicRegistrationConfirmationProps {
	clinicName: string;
	ownerName: string;
	verificationUrl?: string;
	locale?: string;
}

export const ClinicRegistrationConfirmation: React.FC<
	Readonly<ClinicRegistrationConfirmationProps>
> = ({ clinicName, ownerName, verificationUrl, locale = "en" }) => {
	const isRTL = locale === "ar";
	
	const content = {
		en: {
			subject: "Clinic Registration Confirmation",
			greeting: `Dear ${ownerName},`,
			title: "Thank you for registering your clinic!",
			message: `We have received your registration for "${clinicName}" and it is currently under review by our team.`,
			reviewProcess: "What happens next:",
			steps: [
				"Our team will review your clinic information and documentation",
				"We may contact you if additional information is needed",
				"You will receive an email notification once the review is complete",
				"Upon approval, your clinic will be visible to users on our platform"
			],
			timeframe: "The review process typically takes 2-3 business days.",
			contact: "If you have any questions, please don't hesitate to contact our support team.",
			thanks: "Thank you for joining our platform!",
			team: "The Paws & Whiskers Team"
		},
		fr: {
			subject: "Confirmation d'inscription de clinique",
			greeting: `<PERSON><PERSON>/Chère ${ownerName},`,
			title: "Merci d'avoir enregistré votre clinique !",
			message: `Nous avons reçu votre inscription pour "${clinicName}" et elle est actuellement en cours d'examen par notre équipe.`,
			reviewProcess: "Prochaines étapes :",
			steps: [
				"Notre équipe examinera les informations et la documentation de votre clinique",
				"Nous pourrions vous contacter si des informations supplémentaires sont nécessaires",
				"Vous recevrez une notification par email une fois l'examen terminé",
				"Après approbation, votre clinique sera visible aux utilisateurs sur notre plateforme"
			],
			timeframe: "Le processus d'examen prend généralement 2-3 jours ouvrables.",
			contact: "Si vous avez des questions, n'hésitez pas à contacter notre équipe de support.",
			thanks: "Merci de rejoindre notre plateforme !",
			team: "L'équipe Paws & Whiskers"
		},
		ar: {
			subject: "تأكيد تسجيل العيادة",
			greeting: `عزيزي/عزيزتي ${ownerName}،`,
			title: "شكراً لك على تسجيل عيادتك!",
			message: `لقد تلقينا تسجيلك لـ "${clinicName}" وهو قيد المراجعة حالياً من قبل فريقنا.`,
			reviewProcess: "ما سيحدث بعد ذلك:",
			steps: [
				"سيقوم فريقنا بمراجعة معلومات عيادتك والوثائق",
				"قد نتواصل معك إذا كانت هناك حاجة لمعلومات إضافية",
				"ستتلقى إشعاراً بالبريد الإلكتروني بمجرد اكتمال المراجعة",
				"عند الموافقة، ستكون عيادتك مرئية للمستخدمين على منصتنا"
			],
			timeframe: "عملية المراجعة تستغرق عادة 2-3 أيام عمل.",
			contact: "إذا كان لديك أي أسئلة، لا تتردد في الاتصال بفريق الدعم لدينا.",
			thanks: "شكراً لانضمامك إلى منصتنا!",
			team: "فريق Paws & Whiskers"
		}
	};

	const t = content[locale as keyof typeof content] || content.en;

	return (
		<div style={{ 
			fontFamily: 'Arial, sans-serif', 
			maxWidth: '600px', 
			margin: '0 auto',
			direction: isRTL ? 'rtl' : 'ltr'
		}}>
			{/* Header */}
			<div style={{ 
				backgroundColor: '#0ea5e9', 
				color: 'white', 
				padding: '20px', 
				textAlign: 'center' 
			}}>
				<h1 style={{ margin: '0', fontSize: '24px' }}>
					🐾 Paws & Whiskers
				</h1>
			</div>

			{/* Content */}
			<div style={{ padding: '30px', backgroundColor: '#ffffff' }}>
				<p style={{ fontSize: '16px', marginBottom: '10px' }}>
					{t.greeting}
				</p>

				<h2 style={{ 
					color: '#0ea5e9', 
					fontSize: '20px', 
					marginBottom: '20px' 
				}}>
					{t.title}
				</h2>

				<p style={{ 
					fontSize: '16px', 
					lineHeight: '1.6', 
					marginBottom: '20px' 
				}}>
					{t.message}
				</p>

				<div style={{ 
					backgroundColor: '#f8fafc', 
					padding: '20px', 
					borderRadius: '8px', 
					marginBottom: '20px' 
				}}>
					<h3 style={{ 
						color: '#334155', 
						fontSize: '18px', 
						marginBottom: '15px' 
					}}>
						{t.reviewProcess}
					</h3>
					<ol style={{ 
						paddingLeft: isRTL ? '0' : '20px',
						paddingRight: isRTL ? '20px' : '0',
						margin: '0' 
					}}>
						{t.steps.map((step, index) => (
							<li key={index} style={{ 
								marginBottom: '8px', 
								lineHeight: '1.5' 
							}}>
								{step}
							</li>
						))}
					</ol>
				</div>

				<div style={{ 
					backgroundColor: '#ecfdf5', 
					border: '1px solid #10b981', 
					borderRadius: '8px', 
					padding: '15px', 
					marginBottom: '20px' 
				}}>
					<p style={{ 
						color: '#065f46', 
						margin: '0', 
						fontWeight: 'bold' 
					}}>
						⏱️ {t.timeframe}
					</p>
				</div>

				<p style={{ 
					fontSize: '16px', 
					lineHeight: '1.6', 
					marginBottom: '20px' 
				}}>
					{t.contact}
				</p>

				<p style={{ 
					fontSize: '16px', 
					lineHeight: '1.6', 
					marginBottom: '30px' 
				}}>
					{t.thanks}
				</p>

				<p style={{ 
					fontSize: '16px', 
					fontWeight: 'bold', 
					color: '#0ea5e9' 
				}}>
					{t.team}
				</p>
			</div>

			{/* Footer */}
			<div style={{ 
				backgroundColor: '#f1f5f9', 
				padding: '20px', 
				textAlign: 'center', 
				fontSize: '14px', 
				color: '#64748b' 
			}}>
				<p style={{ margin: '0' }}>
					© 2025 Paws & Whiskers. All rights reserved.
				</p>
			</div>
		</div>
	);
};
