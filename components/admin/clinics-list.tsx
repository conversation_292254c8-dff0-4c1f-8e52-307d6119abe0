"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { api } from "@/lib/trpc/react";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import {
	Search,
	MoreHorizontal,
	CheckCircle,
	XCircle,
	Eye,
	Star,
	MapPin,
	Phone,
	Mail,
	Calendar,
	Filter,
	RefreshCw,
} from "lucide-react";
import { format } from "date-fns";

interface AdminClinicsListProps {
	limit?: number;
}

export function AdminClinicsList({ limit }: AdminClinicsListProps) {
	const t = useTranslations("admin.clinics");
	const tCommon = useTranslations("admin.common");
	const { toast } = useToast();

	// State for filters and pagination
	const [search, setSearch] = useState("");
	const [verificationStatus, setVerificationStatus] = useState<string>("all");
	const [isActive, setIsActive] = useState<string>("all");
	const [sortBy, setSortBy] = useState<string>("createdAt");
	const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
	const [page, setPage] = useState(0);
	const [selectedClinic, setSelectedClinic] = useState<number | null>(null);
	const [actionType, setActionType] = useState<
		"verify" | "reject" | "delete" | null
	>(null);

	const pageSize = limit || 20;

	// Fetch clinics with filters
	const {
		data: clinicsData,
		isLoading,
		error,
		refetch,
	} = api.admin.listClinics.useQuery({
		limit: pageSize,
		offset: page * pageSize,
		search: search || undefined,
		verificationStatus:
			verificationStatus === "all"
				? undefined
				: (verificationStatus as any),
		isActive: isActive === "all" ? undefined : isActive === "true",
		sortBy: sortBy as any,
		sortOrder,
	});

	// Mutations
	const verifyClinicMutation = api.admin.verifyClinic.useMutation({
		onSuccess: () => {
			toast({
				title: t("actions.verify"),
				description: "Clinic verification status updated successfully",
			});
			refetch();
			setSelectedClinic(null);
			setActionType(null);
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const toggleStatusMutation = api.admin.toggleClinicStatus.useMutation({
		onSuccess: () => {
			toast({
				title: "Status Updated",
				description: "Clinic status updated successfully",
			});
			refetch();
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const toggleFeaturedMutation = api.admin.toggleClinicFeatured.useMutation({
		onSuccess: () => {
			toast({
				title: "Featured Status Updated",
				description: "Clinic featured status updated successfully",
			});
			refetch();
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const deleteClinicMutation = api.admin.deleteClinic.useMutation({
		onSuccess: () => {
			toast({
				title: "Clinic Deleted",
				description: "Clinic has been permanently deleted",
			});
			refetch();
			setSelectedClinic(null);
			setActionType(null);
		},
		onError: (error) => {
			toast({
				title: tCommon("error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	// Handle verification action
	const handleVerificationAction = (
		clinicId: number,
		status: "verified" | "rejected"
	) => {
		verifyClinicMutation.mutate({
			id: clinicId,
			verificationStatus: status,
			notes:
				status === "rejected"
					? "Rejected by admin"
					: "Verified by admin",
		});
	};

	// Handle delete action
	const handleDeleteClinic = (clinicId: number) => {
		deleteClinicMutation.mutate({ id: clinicId });
	};

	// Get verification status badge
	const getVerificationBadge = (status: string) => {
		switch (status) {
			case "verified":
				return (
					<Badge
						variant="default"
						className="bg-green-100 text-green-800"
					>
						<CheckCircle className="h-3 w-3 mr-1" />
						{t("status.verified")}
					</Badge>
				);
			case "rejected":
				return (
					<Badge variant="destructive">
						<XCircle className="h-3 w-3 mr-1" />
						{t("status.rejected")}
					</Badge>
				);
			default:
				return (
					<Badge variant="secondary">
						<RefreshCw className="h-3 w-3 mr-1" />
						{t("status.pending")}
					</Badge>
				);
		}
	};

	if (error) {
		return (
			<Card>
				<CardContent className="pt-6">
					<div className="text-center text-red-600">
						<p>
							{tCommon("error")}: {error.message}
						</p>
						<Button onClick={() => refetch()} className="mt-2">
							Try Again
						</Button>
					</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<div className="space-y-4">
			{/* Filters and Search */}
			{!limit && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Filter className="h-5 w-5" />
							Filters & Search
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
							{/* Search */}
							<div className="relative">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
								<Input
									placeholder={t("searchPlaceholder")}
									value={search}
									onChange={(e) => setSearch(e.target.value)}
									className="pl-10"
								/>
							</div>

							{/* Verification Status Filter */}
							<Select
								value={verificationStatus}
								onValueChange={setVerificationStatus}
							>
								<SelectTrigger>
									<SelectValue placeholder="Verification Status" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">
										All Statuses
									</SelectItem>
									<SelectItem value="pending">
										Pending
									</SelectItem>
									<SelectItem value="verified">
										Verified
									</SelectItem>
									<SelectItem value="rejected">
										Rejected
									</SelectItem>
								</SelectContent>
							</Select>

							{/* Active Status Filter */}
							<Select
								value={isActive}
								onValueChange={setIsActive}
							>
								<SelectTrigger>
									<SelectValue placeholder="Active Status" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">
										All Clinics
									</SelectItem>
									<SelectItem value="true">
										Active Only
									</SelectItem>
									<SelectItem value="false">
										Inactive Only
									</SelectItem>
								</SelectContent>
							</Select>

							{/* Sort Options */}
							<Select
								value={`${sortBy}-${sortOrder}`}
								onValueChange={(value) => {
									const [field, order] = value.split("-");
									setSortBy(field);
									setSortOrder(order as "asc" | "desc");
								}}
							>
								<SelectTrigger>
									<SelectValue placeholder="Sort By" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="createdAt-desc">
										Newest First
									</SelectItem>
									<SelectItem value="createdAt-asc">
										Oldest First
									</SelectItem>
									<SelectItem value="name-asc">
										Name A-Z
									</SelectItem>
									<SelectItem value="name-desc">
										Name Z-A
									</SelectItem>
									<SelectItem value="ratingAverage-desc">
										Highest Rated
									</SelectItem>
									<SelectItem value="verificationStatus-asc">
										Verification Status
									</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</CardContent>
				</Card>
			)}

			{/* Clinics Table */}
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<CardTitle>{t("title")}</CardTitle>
						<Button
							variant="outline"
							size="sm"
							onClick={() => refetch()}
							disabled={isLoading}
						>
							<RefreshCw
								className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
							/>
							Refresh
						</Button>
					</div>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="flex items-center justify-center min-h-[400px]">
							<div className="text-center space-y-2">
								<RefreshCw className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
								<p className="text-muted-foreground">
									Loading clinics...
								</p>
							</div>
						</div>
					) : !clinicsData?.clinics?.length ? (
						<div className="flex items-center justify-center min-h-[400px]">
							<div className="text-center space-y-2">
								<p className="text-lg font-medium text-muted-foreground">
									{t("noClinics")}
								</p>
								<p className="text-sm text-muted-foreground">
									No clinics match your current filters
								</p>
							</div>
						</div>
					) : (
						<>
							<div className="rounded-md border">
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>
												{t("columns.clinic")}
											</TableHead>
											<TableHead>
												{t("columns.location")}
											</TableHead>
											<TableHead>
												{t("columns.contact")}
											</TableHead>
											<TableHead>
												{t("columns.status")}
											</TableHead>
											<TableHead>Rating</TableHead>
											<TableHead>Created</TableHead>
											<TableHead className="text-right">
												{t("columns.actions")}
											</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{clinicsData.clinics.map((clinic) => (
											<TableRow
												key={clinic.id}
												className="hover:bg-muted/50"
											>
												<TableCell>
													<div className="space-y-1">
														<div className="flex items-center gap-2">
															<p className="font-medium">
																{clinic.name}
															</p>
															{clinic.featured && (
																<Star className="h-4 w-4 text-yellow-500 fill-current" />
															)}
														</div>
														{clinic.description && (
															<p className="text-sm text-muted-foreground line-clamp-1">
																{
																	clinic.description
																}
															</p>
														)}
													</div>
												</TableCell>
												<TableCell>
													<div className="space-y-1">
														{clinic.address && (
															<div className="flex items-center gap-1 text-sm">
																<MapPin className="h-3 w-3" />
																<span className="line-clamp-1">
																	{
																		clinic.address
																	}
																</span>
															</div>
														)}
														{(clinic.city ||
															clinic.state) && (
															<p className="text-sm text-muted-foreground">
																{[
																	clinic.city,
																	clinic.state,
																]
																	.filter(
																		Boolean
																	)
																	.join(", ")}
															</p>
														)}
													</div>
												</TableCell>
												<TableCell>
													<div className="space-y-1">
														{clinic.phone && (
															<div className="flex items-center gap-1 text-sm">
																<Phone className="h-3 w-3" />
																<span>
																	{
																		clinic.phone
																	}
																</span>
															</div>
														)}
														{clinic.email && (
															<div className="flex items-center gap-1 text-sm">
																<Mail className="h-3 w-3" />
																<span className="line-clamp-1">
																	{
																		clinic.email
																	}
																</span>
															</div>
														)}
													</div>
												</TableCell>
												<TableCell>
													<div className="space-y-2">
														{getVerificationBadge(
															clinic.verificationStatus
														)}
														<div className="flex items-center gap-1">
															<Badge
																variant={
																	clinic.isActive
																		? "default"
																		: "secondary"
																}
															>
																{clinic.isActive
																	? "Active"
																	: "Inactive"}
															</Badge>
														</div>
													</div>
												</TableCell>
												<TableCell>
													<div className="text-sm">
														{clinic.ratingAverage ? (
															<div className="flex items-center gap-1">
																<Star className="h-3 w-3 text-yellow-500 fill-current" />
																<span>
																	{clinic.ratingAverage.toFixed(
																		1
																	)}
																</span>
																<span className="text-muted-foreground">
																	(
																	{
																		clinic.reviewCount
																	}
																	)
																</span>
															</div>
														) : (
															<span className="text-muted-foreground">
																No reviews
															</span>
														)}
													</div>
												</TableCell>
												<TableCell>
													<div className="flex items-center gap-1 text-sm text-muted-foreground">
														<Calendar className="h-3 w-3" />
														<span>
															{format(
																new Date(
																	clinic.createdAt
																),
																"MMM dd, yyyy"
															)}
														</span>
													</div>
												</TableCell>
												<TableCell className="text-right">
													<DropdownMenu>
														<DropdownMenuTrigger
															asChild
														>
															<Button
																variant="ghost"
																className="h-8 w-8 p-0"
															>
																<span className="sr-only">
																	Open menu
																</span>
																<MoreHorizontal className="h-4 w-4" />
															</Button>
														</DropdownMenuTrigger>
														<DropdownMenuContent align="end">
															<DropdownMenuLabel>
																Actions
															</DropdownMenuLabel>
															<DropdownMenuItem
																onClick={() =>
																	window.open(
																		`/clinics/${clinic.slug}`,
																		"_blank"
																	)
																}
															>
																<Eye className="mr-2 h-4 w-4" />
																{t(
																	"actions.viewProfile"
																)}
															</DropdownMenuItem>
															<DropdownMenuSeparator />
															{clinic.verificationStatus ===
																"pending" && (
																<>
																	<DropdownMenuItem
																		onClick={() => {
																			setSelectedClinic(
																				clinic.id
																			);
																			setActionType(
																				"verify"
																			);
																		}}
																		className="text-green-600"
																	>
																		<CheckCircle className="mr-2 h-4 w-4" />
																		Verify
																		Clinic
																	</DropdownMenuItem>
																	<DropdownMenuItem
																		onClick={() => {
																			setSelectedClinic(
																				clinic.id
																			);
																			setActionType(
																				"reject"
																			);
																		}}
																		className="text-red-600"
																	>
																		<XCircle className="mr-2 h-4 w-4" />
																		Reject
																		Clinic
																	</DropdownMenuItem>
																</>
															)}
															{clinic.verificationStatus ===
																"verified" && (
																<DropdownMenuItem
																	onClick={() => {
																		setSelectedClinic(
																			clinic.id
																		);
																		setActionType(
																			"reject"
																		);
																	}}
																	className="text-red-600"
																>
																	<XCircle className="mr-2 h-4 w-4" />
																	Unverify
																	Clinic
																</DropdownMenuItem>
															)}
															{clinic.verificationStatus ===
																"rejected" && (
																<DropdownMenuItem
																	onClick={() => {
																		setSelectedClinic(
																			clinic.id
																		);
																		setActionType(
																			"verify"
																		);
																	}}
																	className="text-green-600"
																>
																	<CheckCircle className="mr-2 h-4 w-4" />
																	Verify
																	Clinic
																</DropdownMenuItem>
															)}
															<DropdownMenuItem
																onClick={() =>
																	toggleStatusMutation.mutate(
																		{
																			id: clinic.id,
																		}
																	)
																}
															>
																{clinic.isActive
																	? "Deactivate"
																	: "Activate"}
															</DropdownMenuItem>
															<DropdownMenuItem
																onClick={() =>
																	toggleFeaturedMutation.mutate(
																		{
																			id: clinic.id,
																		}
																	)
																}
															>
																<Star className="mr-2 h-4 w-4" />
																{clinic.featured
																	? "Unfeature"
																	: "Feature"}
															</DropdownMenuItem>
															<DropdownMenuSeparator />
															<DropdownMenuItem
																onClick={() => {
																	setSelectedClinic(
																		clinic.id
																	);
																	setActionType(
																		"delete"
																	);
																}}
																className="text-red-600"
															>
																{t(
																	"actions.delete"
																)}
															</DropdownMenuItem>
														</DropdownMenuContent>
													</DropdownMenu>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							</div>

							{/* Pagination */}
							{!limit && clinicsData.total > pageSize && (
								<div className="flex items-center justify-between pt-4">
									<p className="text-sm text-muted-foreground">
										Showing {page * pageSize + 1} to{" "}
										{Math.min(
											(page + 1) * pageSize,
											clinicsData.total
										)}{" "}
										of {clinicsData.total} clinics
									</p>
									<div className="flex items-center space-x-2">
										<Button
											variant="outline"
											size="sm"
											onClick={() => setPage(page - 1)}
											disabled={page === 0}
										>
											Previous
										</Button>
										<Button
											variant="outline"
											size="sm"
											onClick={() => setPage(page + 1)}
											disabled={
												(page + 1) * pageSize >=
												clinicsData.total
											}
										>
											Next
										</Button>
									</div>
								</div>
							)}
						</>
					)}
				</CardContent>
			</Card>

			{/* Confirmation Dialogs */}
			<AlertDialog
				open={!!selectedClinic && !!actionType}
				onOpenChange={() => {
					setSelectedClinic(null);
					setActionType(null);
				}}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							{actionType === "verify" && "Verify Clinic"}
							{actionType === "reject" && "Reject Clinic"}
							{actionType === "delete" && "Delete Clinic"}
						</AlertDialogTitle>
						<AlertDialogDescription>
							{actionType === "verify" &&
								"Are you sure you want to verify this clinic? This will mark it as verified and make it visible to users."}
							{actionType === "reject" &&
								"Are you sure you want to reject this clinic? This will mark it as rejected and hide it from users."}
							{actionType === "delete" &&
								"Are you sure you want to delete this clinic? This action cannot be undone and will permanently remove all clinic data."}
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={() => {
								if (!selectedClinic) return;

								if (actionType === "verify") {
									handleVerificationAction(
										selectedClinic,
										"verified"
									);
								} else if (actionType === "reject") {
									handleVerificationAction(
										selectedClinic,
										"rejected"
									);
								} else if (actionType === "delete") {
									handleDeleteClinic(selectedClinic);
								}
							}}
							className={
								actionType === "delete"
									? "bg-red-600 hover:bg-red-700"
									: ""
							}
						>
							{actionType === "verify" && "Verify"}
							{actionType === "reject" && "Reject"}
							{actionType === "delete" && "Delete"}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
}
