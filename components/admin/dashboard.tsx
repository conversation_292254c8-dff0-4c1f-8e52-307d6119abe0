"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { AdminCatsList } from "@/components/admin/cats-list";
import { AdminUsersList } from "@/components/admin/users-list";
import { AdminClinicsList } from "@/components/admin/clinics-list";
import { AdminStats } from "@/components/admin/stats";
import { api } from "@/lib/trpc/react";

export function AdminDashboard() {
	const t = useTranslations("admin");
	const [activeTab, setActiveTab] = useState("overview");

	// Fetch dashboard stats
	const { data: stats, isLoading: statsLoading } =
		api.admin.getStats.useQuery();

	return (
		<div className="space-y-6">
			<Tabs
				defaultValue="overview"
				value={activeTab}
				onValueChange={setActiveTab}
			>
				<TabsList className="grid w-full grid-cols-4">
					<TabsTrigger value="overview">
						{t("tabs.overview")}
					</TabsTrigger>
					<TabsTrigger value="cats">{t("tabs.cats")}</TabsTrigger>
					<TabsTrigger value="users">{t("tabs.users")}</TabsTrigger>
					<TabsTrigger value="clinics">
						{t("tabs.clinics")}
					</TabsTrigger>
				</TabsList>

				<TabsContent value="overview">
					<div className="space-y-6">
						{/* Quick Stats Cards */}
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
							<Card>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">
										{t("stats.totalUsers")}
									</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="text-2xl font-bold">
										{statsLoading
											? "..."
											: stats?.totalUsers || 0}
									</div>
									<p className="text-xs text-muted-foreground">
										+
										{statsLoading
											? "..."
											: stats?.recentUsers || 0}{" "}
										{t("stats.thisWeek")}
									</p>
								</CardContent>
							</Card>
							<Card>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">
										{t("stats.totalCats")}
									</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="text-2xl font-bold">
										{statsLoading
											? "..."
											: stats?.totalCats || 0}
									</div>
									<p className="text-xs text-muted-foreground">
										+
										{statsLoading
											? "..."
											: stats?.recentCats || 0}{" "}
										{t("stats.thisWeek")}
									</p>
								</CardContent>
							</Card>
							<Card>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">
										{t("stats.totalMessages")}
									</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="text-2xl font-bold">
										{statsLoading
											? "..."
											: stats?.totalMessages || 0}
									</div>
								</CardContent>
							</Card>
							<Card>
								<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
									<CardTitle className="text-sm font-medium">
										{t("stats.totalFavorites")}
									</CardTitle>
								</CardHeader>
								<CardContent>
									<div className="text-2xl font-bold">
										{statsLoading
											? "..."
											: stats?.totalFavorites || 0}
									</div>
								</CardContent>
							</Card>
						</div>

						{/* Charts and Analytics */}
						<AdminStats />

						{/* Recent Activity */}
						<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
							<Card>
								<CardHeader>
									<CardTitle>
										{t("recentCats.title")}
									</CardTitle>
									<CardDescription>
										{t("recentCats.description")}
									</CardDescription>
								</CardHeader>
								<CardContent>
									<AdminCatsList limit={5} />
								</CardContent>
							</Card>

							<Card>
								<CardHeader>
									<CardTitle>
										{t("recentUsers.title")}
									</CardTitle>
									<CardDescription>
										{t("recentUsers.description")}
									</CardDescription>
								</CardHeader>
								<CardContent>
									<AdminUsersList limit={5} />
								</CardContent>
							</Card>
						</div>
					</div>
				</TabsContent>

				<TabsContent value="cats">
					<Card>
						<CardHeader>
							<CardTitle>{t("cats.title")}</CardTitle>
							<CardDescription>
								{t("cats.description")}
							</CardDescription>
						</CardHeader>
						<CardContent>
							<AdminCatsList />
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="users">
					<Card>
						<CardHeader>
							<CardTitle>{t("users.title")}</CardTitle>
							<CardDescription>
								{t("users.description")}
							</CardDescription>
						</CardHeader>
						<CardContent>
							<AdminUsersList />
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="clinics">
					<Card>
						<CardHeader>
							<CardTitle>{t("clinics.title")}</CardTitle>
							<CardDescription>
								{t("clinics.description")}
							</CardDescription>
						</CardHeader>
						<CardContent>
							<AdminClinicsList />
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
		</div>
	);
}
