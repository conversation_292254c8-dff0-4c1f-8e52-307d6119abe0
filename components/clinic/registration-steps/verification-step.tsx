"use client";

import { UseFormReturn } from "react-hook-form";
import {
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { X, Plus, Upload } from "lucide-react";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { ClinicRegistrationData } from "../clinic-registration-form";

interface VerificationStepProps {
	form: UseFormReturn<ClinicRegistrationData>;
}

export function VerificationStep({ form }: VerificationStepProps) {
	const t = useTranslations("clinic.registration.steps.verification");
	const [newImage, setNewImage] = useState("");

	const images = form.watch("images") || [];
	const socialMedia = form.watch("socialMedia") || {};

	// Add image URL
	const addImage = (imageUrl: string) => {
		if (imageUrl && !images.includes(imageUrl)) {
			form.setValue("images", [...images, imageUrl]);
		}
		setNewImage("");
	};

	// Remove image
	const removeImage = (imageUrl: string) => {
		form.setValue(
			"images",
			images.filter((img) => img !== imageUrl)
		);
	};

	return (
		<div className="space-y-8">
			{/* Images Section */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Upload className="h-5 w-5" />
						{t("images")}
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<p className="text-sm text-muted-foreground">
						{t("imagesDescription")}
					</p>

					{/* Add Image URL */}
					<div className="space-y-2">
						<p className="text-sm font-medium">{t("addImageUrl")}</p>
						<div className="flex gap-2">
							<Input
								placeholder={t("imageUrlPlaceholder")}
								value={newImage}
								onChange={(e) => setNewImage(e.target.value)}
								onKeyPress={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										addImage(newImage);
									}
								}}
							/>
							<Button
								type="button"
								variant="outline"
								size="icon"
								onClick={() => addImage(newImage)}
								disabled={!newImage.trim()}
							>
								<Plus className="h-4 w-4" />
							</Button>
						</div>
					</div>

					{/* Selected Images */}
					{images.length > 0 && (
						<div className="space-y-2">
							<p className="text-sm font-medium">{t("selectedImages")}</p>
							<div className="grid grid-cols-1 gap-2 md:grid-cols-2">
								{images.map((imageUrl, index) => (
									<div
										key={index}
										className="flex items-center justify-between rounded-lg border p-3"
									>
										<div className="flex items-center gap-3">
											<img
												src={imageUrl}
												alt={`Clinic image ${index + 1}`}
												className="h-12 w-12 rounded object-cover"
												onError={(e) => {
													e.currentTarget.src = "/placeholder-image.jpg";
												}}
											/>
											<span className="text-sm truncate">
												{imageUrl.length > 40
													? `${imageUrl.substring(0, 40)}...`
													: imageUrl}
											</span>
										</div>
										<Button
											type="button"
											variant="ghost"
											size="sm"
											onClick={() => removeImage(imageUrl)}
											className="text-destructive hover:text-destructive"
										>
											<X className="h-4 w-4" />
										</Button>
									</div>
								))}
							</div>
						</div>
					)}

					<div className="rounded-lg bg-blue-50 p-4">
						<p className="text-sm text-blue-800">
							{t("imagesNote")}
						</p>
					</div>
				</CardContent>
			</Card>

			{/* Social Media Section */}
			<Card>
				<CardHeader>
					<CardTitle>{t("socialMedia")}</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<p className="text-sm text-muted-foreground">
						{t("socialMediaDescription")}
					</p>

					<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
						{/* Facebook */}
						<FormField
							control={form.control}
							name="socialMedia.facebook"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("facebook")}</FormLabel>
									<FormControl>
										<Input
											placeholder="https://facebook.com/yourclinic"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Instagram */}
						<FormField
							control={form.control}
							name="socialMedia.instagram"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("instagram")}</FormLabel>
									<FormControl>
										<Input
											placeholder="https://instagram.com/yourclinic"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						{/* Twitter */}
						<FormField
							control={form.control}
							name="socialMedia.twitter"
							render={({ field }) => (
								<FormItem>
									<FormLabel>{t("twitter")}</FormLabel>
									<FormControl>
										<Input
											placeholder="https://twitter.com/yourclinic"
											{...field}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					<div className="rounded-lg bg-green-50 p-4">
						<p className="text-sm text-green-800">
							{t("socialMediaNote")}
						</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
