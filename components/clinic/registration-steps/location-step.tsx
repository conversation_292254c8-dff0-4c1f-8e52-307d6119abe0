"use client";

import { UseFormReturn } from "react-hook-form";
import {
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON>lider } from "@/components/ui/slider";
import { useTranslations } from "next-intl";
import { ClinicRegistrationData } from "../clinic-registration-form";

interface LocationStepProps {
	form: UseFormReturn<ClinicRegistrationData>;
}

export function LocationStep({ form }: LocationStepProps) {
	const t = useTranslations("clinic.registration.steps.location");

	const serviceAreaRadius = form.watch("serviceAreaRadius") || 10;

	return (
		<div className="space-y-6">
			{/* Address */}
			<FormField
				control={form.control}
				name="address"
				render={({ field }) => (
					<FormItem>
						<FormLabel className="required">{t("address")}</FormLabel>
						<FormControl>
							<Input
								placeholder={t("addressPlaceholder")}
								{...field}
							/>
						</FormControl>
						<FormDescription>
							{t("addressDescription")}
						</FormDescription>
						<FormMessage />
					</FormItem>
				)}
			/>

			<div className="grid grid-cols-1 gap-6 md:grid-cols-3">
				{/* City */}
				<FormField
					control={form.control}
					name="city"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="required">{t("city")}</FormLabel>
							<FormControl>
								<Input
									placeholder={t("cityPlaceholder")}
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* State */}
				<FormField
					control={form.control}
					name="state"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="required">{t("state")}</FormLabel>
							<FormControl>
								<Input
									placeholder={t("statePlaceholder")}
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Postal Code */}
				<FormField
					control={form.control}
					name="postalCode"
					render={({ field }) => (
						<FormItem>
							<FormLabel>{t("postalCode")}</FormLabel>
							<FormControl>
								<Input
									placeholder={t("postalCodePlaceholder")}
									{...field}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Service Area Radius */}
			<FormField
				control={form.control}
				name="serviceAreaRadius"
				render={({ field }) => (
					<FormItem>
						<FormLabel>{t("serviceAreaRadius")}</FormLabel>
						<FormControl>
							<div className="space-y-4">
								<Slider
									min={1}
									max={100}
									step={1}
									value={[field.value || 10]}
									onValueChange={(value) => field.onChange(value[0])}
									className="w-full"
								/>
								<div className="flex justify-between text-sm text-muted-foreground">
									<span>1 km</span>
									<span className="font-medium">
										{serviceAreaRadius} km
									</span>
									<span>100 km</span>
								</div>
							</div>
						</FormControl>
						<FormDescription>
							{t("serviceAreaDescription")}
						</FormDescription>
						<FormMessage />
					</FormItem>
				)}
			/>

			{/* Coordinates Section */}
			<div className="rounded-lg border p-4">
				<h4 className="mb-4 font-medium">{t("coordinates")}</h4>
				<p className="mb-4 text-sm text-muted-foreground">
					{t("coordinatesDescription")}
				</p>
				
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
					{/* Latitude */}
					<FormField
						control={form.control}
						name="coordinates.lat"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{t("latitude")}</FormLabel>
								<FormControl>
									<Input
										type="number"
										step="any"
										placeholder="0.000000"
										{...field}
										onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Longitude */}
					<FormField
						control={form.control}
						name="coordinates.lng"
						render={({ field }) => (
							<FormItem>
								<FormLabel>{t("longitude")}</FormLabel>
								<FormControl>
									<Input
										type="number"
										step="any"
										placeholder="0.000000"
										{...field}
										onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="mt-4">
					<button
						type="button"
						className="text-sm text-primary hover:underline"
						onClick={() => {
							if (navigator.geolocation) {
								navigator.geolocation.getCurrentPosition(
									(position) => {
										form.setValue("coordinates.lat", position.coords.latitude);
										form.setValue("coordinates.lng", position.coords.longitude);
									},
									(error) => {
										console.error("Error getting location:", error);
									}
								);
							}
						}}
					>
						{t("useCurrentLocation")}
					</button>
				</div>
			</div>
		</div>
	);
}
