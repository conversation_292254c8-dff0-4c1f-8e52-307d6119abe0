"use client";

import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import {
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, Plus } from "lucide-react";
import { useTranslations } from "next-intl";
import { ClinicRegistrationData } from "../clinic-registration-form";
import { ServiceSetupWizard, ServiceData } from "../service-setup-wizard";
import { Separator } from "@/components/ui/separator";

interface ServicesStepProps {
	form: UseFormReturn<ClinicRegistrationData>;
	services?: ServiceData[];
	onServicesChange?: (services: ServiceData[]) => void;
}

// Predefined specializations
const PREDEFINED_SPECIALIZATIONS = [
	"general_practice",
	"surgery",
	"dental_care",
	"emergency_care",
	"vaccination",
	"grooming",
	"behavioral_therapy",
	"nutrition_counseling",
	"diagnostic_imaging",
	"laboratory_services",
	"spay_neuter",
	"microchipping",
	"wellness_exams",
	"senior_care",
	"exotic_animals",
];

// Predefined amenities
const PREDEFINED_AMENITIES = [
	"parking",
	"wheelchair_accessible",
	"air_conditioning",
	"waiting_room",
	"separate_cat_dog_areas",
	"isolation_rooms",
	"surgical_suite",
	"x_ray_equipment",
	"laboratory",
	"pharmacy",
	"grooming_facilities",
	"boarding",
	"24_hour_emergency",
	"online_booking",
	"telemedicine",
];

export function ServicesStep({
	form,
	services = [],
	onServicesChange,
}: ServicesStepProps) {
	const t = useTranslations("clinic.registration.steps.services");
	const [newSpecialization, setNewSpecialization] = useState("");
	const [newAmenity, setNewAmenity] = useState("");

	const specializations = form.watch("specializations") || [];
	const amenities = form.watch("amenities") || [];

	// Add specialization
	const addSpecialization = (specialization: string) => {
		if (specialization && !specializations.includes(specialization)) {
			form.setValue("specializations", [
				...specializations,
				specialization,
			]);
		}
		setNewSpecialization("");
	};

	// Remove specialization
	const removeSpecialization = (specialization: string) => {
		form.setValue(
			"specializations",
			specializations.filter((s) => s !== specialization)
		);
	};

	// Add amenity
	const addAmenity = (amenity: string) => {
		if (amenity && !amenities.includes(amenity)) {
			form.setValue("amenities", [...amenities, amenity]);
		}
		setNewAmenity("");
	};

	// Remove amenity
	const removeAmenity = (amenity: string) => {
		form.setValue(
			"amenities",
			amenities.filter((a) => a !== amenity)
		);
	};

	return (
		<div className="space-y-8">
			{/* Specializations */}
			<div className="space-y-4">
				<div>
					<h4 className="text-lg font-medium">
						{t("specializations")}
					</h4>
					<p className="text-sm text-muted-foreground">
						{t("specializationsDescription")}
					</p>
				</div>

				{/* Predefined Specializations */}
				<div className="space-y-2">
					<p className="text-sm font-medium">
						{t("commonSpecializations")}
					</p>
					<div className="flex flex-wrap gap-2">
						{PREDEFINED_SPECIALIZATIONS.map((spec) => (
							<Button
								key={spec}
								type="button"
								variant={
									specializations.includes(spec)
										? "default"
										: "outline"
								}
								size="sm"
								onClick={() => {
									if (specializations.includes(spec)) {
										removeSpecialization(spec);
									} else {
										addSpecialization(spec);
									}
								}}
							>
								{t(`specializationOptions.${spec}`)}
							</Button>
						))}
					</div>
				</div>

				{/* Custom Specialization */}
				<div className="space-y-2">
					<p className="text-sm font-medium">
						{t("customSpecialization")}
					</p>
					<div className="flex gap-2">
						<Input
							placeholder={t("customSpecializationPlaceholder")}
							value={newSpecialization}
							onChange={(e) =>
								setNewSpecialization(e.target.value)
							}
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									e.preventDefault();
									addSpecialization(newSpecialization);
								}
							}}
						/>
						<Button
							type="button"
							variant="outline"
							size="icon"
							onClick={() => addSpecialization(newSpecialization)}
							disabled={!newSpecialization.trim()}
						>
							<Plus className="h-4 w-4" />
						</Button>
					</div>
				</div>

				{/* Selected Specializations */}
				{specializations.length > 0 && (
					<div className="space-y-2">
						<p className="text-sm font-medium">
							{t("selectedSpecializations")}
						</p>
						<div className="flex flex-wrap gap-2">
							{specializations.map((spec) => (
								<Badge
									key={spec}
									variant="secondary"
									className="gap-1"
								>
									{t(`specializationOptions.${spec}`, {
										defaultValue: spec,
									})}
									<button
										type="button"
										onClick={() =>
											removeSpecialization(spec)
										}
										className="ml-1 hover:text-destructive"
									>
										<X className="h-3 w-3" />
									</button>
								</Badge>
							))}
						</div>
					</div>
				)}
			</div>

			{/* Amenities */}
			<div className="space-y-4">
				<div>
					<h4 className="text-lg font-medium">{t("amenities")}</h4>
					<p className="text-sm text-muted-foreground">
						{t("amenitiesDescription")}
					</p>
				</div>

				{/* Predefined Amenities */}
				<div className="space-y-2">
					<p className="text-sm font-medium">
						{t("commonAmenities")}
					</p>
					<div className="flex flex-wrap gap-2">
						{PREDEFINED_AMENITIES.map((amenity) => (
							<Button
								key={amenity}
								type="button"
								variant={
									amenities.includes(amenity)
										? "default"
										: "outline"
								}
								size="sm"
								onClick={() => {
									if (amenities.includes(amenity)) {
										removeAmenity(amenity);
									} else {
										addAmenity(amenity);
									}
								}}
							>
								{t(`amenityOptions.${amenity}`)}
							</Button>
						))}
					</div>
				</div>

				{/* Custom Amenity */}
				<div className="space-y-2">
					<p className="text-sm font-medium">{t("customAmenity")}</p>
					<div className="flex gap-2">
						<Input
							placeholder={t("customAmenityPlaceholder")}
							value={newAmenity}
							onChange={(e) => setNewAmenity(e.target.value)}
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									e.preventDefault();
									addAmenity(newAmenity);
								}
							}}
						/>
						<Button
							type="button"
							variant="outline"
							size="icon"
							onClick={() => addAmenity(newAmenity)}
							disabled={!newAmenity.trim()}
						>
							<Plus className="h-4 w-4" />
						</Button>
					</div>
				</div>

				{/* Selected Amenities */}
				{amenities.length > 0 && (
					<div className="space-y-2">
						<p className="text-sm font-medium">
							{t("selectedAmenities")}
						</p>
						<div className="flex flex-wrap gap-2">
							{amenities.map((amenity) => (
								<Badge
									key={amenity}
									variant="secondary"
									className="gap-1"
								>
									{t(`amenityOptions.${amenity}`, {
										defaultValue: amenity,
									})}
									<button
										type="button"
										onClick={() => removeAmenity(amenity)}
										className="ml-1 hover:text-destructive"
									>
										<X className="h-3 w-3" />
									</button>
								</Badge>
							))}
						</div>
					</div>
				)}
			</div>

			{/* Service Setup Wizard - Temporarily disabled due to form context conflict */}
			<div className="space-y-4">
				<Separator />
				<div className="rounded-lg bg-yellow-50 p-4">
					<p className="text-sm text-yellow-800">
						{t("servicesWizardNote", {
							defaultValue:
								"Service setup will be available after registration completion. You can add services from your clinic dashboard.",
						})}
					</p>
				</div>
				{/* <ServiceSetupWizard
					services={services}
					onServicesChange={onServicesChange}
				/> */}
			</div>

			{/* Note */}
			<div className="rounded-lg bg-blue-50 p-4">
				<p className="text-sm text-blue-800">{t("servicesNote")}</p>
			</div>
		</div>
	);
}
