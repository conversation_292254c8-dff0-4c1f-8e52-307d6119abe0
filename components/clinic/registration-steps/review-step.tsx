"use client";

import { UseFormReturn } from "react-hook-form";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
	MapPin, 
	Phone, 
	Mail, 
	Globe, 
	Clock, 
	Users, 
	Star,
	CheckCircle,
	AlertCircle
} from "lucide-react";
import { useTranslations } from "next-intl";
import { ClinicRegistrationData } from "../clinic-registration-form";

interface ReviewStepProps {
	form: UseFormReturn<ClinicRegistrationData>;
}

export function ReviewStep({ form }: ReviewStepProps) {
	const t = useTranslations("clinic.registration.steps.review");
	const tSteps = useTranslations("clinic.registration.steps");
	
	const formData = form.getValues();

	// Get specialization display name
	const getSpecializationName = (spec: string) => {
		return tSteps(`services.specializationOptions.${spec}`);
	};

	// Get amenity display name
	const getAmenityName = (amenity: string) => {
		return tSteps(`services.amenityOptions.${amenity}`);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="text-center space-y-2">
				<h3 className="text-lg font-semibold">{t("title")}</h3>
				<p className="text-muted-foreground">{t("description")}</p>
			</div>

			{/* Basic Information */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<CheckCircle className="h-5 w-5 text-green-600" />
						{tSteps("basicInfo.title")}
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div>
							<p className="text-sm font-medium text-muted-foreground">{tSteps("basicInfo.name")}</p>
							<p className="font-medium">{formData.name}</p>
						</div>
						<div>
							<p className="text-sm font-medium text-muted-foreground">{tSteps("basicInfo.email")}</p>
							<p className="flex items-center gap-2">
								<Mail className="h-4 w-4" />
								{formData.email}
							</p>
						</div>
						<div>
							<p className="text-sm font-medium text-muted-foreground">{tSteps("basicInfo.phone")}</p>
							<p className="flex items-center gap-2">
								<Phone className="h-4 w-4" />
								{formData.phone}
							</p>
						</div>
						{formData.website && (
							<div>
								<p className="text-sm font-medium text-muted-foreground">{tSteps("basicInfo.website")}</p>
								<p className="flex items-center gap-2">
									<Globe className="h-4 w-4" />
									{formData.website}
								</p>
							</div>
						)}
					</div>
					{formData.description && (
						<div>
							<p className="text-sm font-medium text-muted-foreground">{tSteps("basicInfo.description")}</p>
							<p className="text-sm">{formData.description}</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Location Information */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<MapPin className="h-5 w-5 text-blue-600" />
						{tSteps("location.title")}
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						<div>
							<p className="text-sm font-medium text-muted-foreground">{tSteps("location.address")}</p>
							<p>{formData.address}</p>
						</div>
						<div>
							<p className="text-sm font-medium text-muted-foreground">{tSteps("location.city")}</p>
							<p>{formData.city}</p>
						</div>
						<div>
							<p className="text-sm font-medium text-muted-foreground">{tSteps("location.state")}</p>
							<p>{formData.state}</p>
						</div>
						<div>
							<p className="text-sm font-medium text-muted-foreground">{tSteps("location.postalCode")}</p>
							<p>{formData.postalCode}</p>
						</div>
					</div>
					
					{formData.serviceAreaRadius && (
						<div>
							<p className="text-sm font-medium text-muted-foreground">{tSteps("location.serviceAreaRadius")}</p>
							<p>{formData.serviceAreaRadius} km</p>
						</div>
					)}

					{(formData.latitude || formData.longitude) && (
						<div>
							<p className="text-sm font-medium text-muted-foreground">{tSteps("location.coordinates")}</p>
							<p>{formData.latitude}, {formData.longitude}</p>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Services Information */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Star className="h-5 w-5 text-purple-600" />
						{tSteps("services.title")}
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{/* Specializations */}
					{formData.specializations && formData.specializations.length > 0 && (
						<div>
							<p className="text-sm font-medium text-muted-foreground mb-2">
								{tSteps("services.specializations")}
							</p>
							<div className="flex flex-wrap gap-2">
								{formData.specializations.map((spec, index) => (
									<Badge key={index} variant="secondary">
										{getSpecializationName(spec)}
									</Badge>
								))}
							</div>
						</div>
					)}

					{/* Amenities */}
					{formData.amenities && formData.amenities.length > 0 && (
						<div>
							<p className="text-sm font-medium text-muted-foreground mb-2">
								{tSteps("services.amenities")}
							</p>
							<div className="flex flex-wrap gap-2">
								{formData.amenities.map((amenity, index) => (
									<Badge key={index} variant="outline">
										{getAmenityName(amenity)}
									</Badge>
								))}
							</div>
						</div>
					)}
				</CardContent>
			</Card>

			{/* Additional Information */}
			{(formData.images?.length || formData.socialMedia) && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Users className="h-5 w-5 text-orange-600" />
							{t("additionalInfo")}
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						{/* Images */}
						{formData.images && formData.images.length > 0 && (
							<div>
								<p className="text-sm font-medium text-muted-foreground mb-2">
									{t("images")} ({formData.images.length})
								</p>
								<div className="grid grid-cols-2 gap-2 md:grid-cols-4">
									{formData.images.slice(0, 4).map((imageUrl, index) => (
										<img
											key={index}
											src={imageUrl}
											alt={`Clinic image ${index + 1}`}
											className="h-20 w-full rounded object-cover"
											onError={(e) => {
												e.currentTarget.src = "/placeholder-image.jpg";
											}}
										/>
									))}
								</div>
								{formData.images.length > 4 && (
									<p className="text-sm text-muted-foreground">
										{t("andMoreImages", { count: formData.images.length - 4 })}
									</p>
								)}
							</div>
						)}

						{/* Social Media */}
						{formData.socialMedia && (
							<div>
								<p className="text-sm font-medium text-muted-foreground mb-2">
									{t("socialMedia")}
								</p>
								<div className="space-y-1">
									{formData.socialMedia.facebook && (
										<p className="text-sm">Facebook: {formData.socialMedia.facebook}</p>
									)}
									{formData.socialMedia.instagram && (
										<p className="text-sm">Instagram: {formData.socialMedia.instagram}</p>
									)}
									{formData.socialMedia.twitter && (
										<p className="text-sm">Twitter: {formData.socialMedia.twitter}</p>
									)}
								</div>
							</div>
						)}
					</CardContent>
				</Card>
			)}

			{/* Important Notice */}
			<Card className="border-amber-200 bg-amber-50">
				<CardContent className="pt-6">
					<div className="flex items-start gap-3">
						<AlertCircle className="h-5 w-5 text-amber-600 mt-0.5" />
						<div className="space-y-2">
							<p className="font-medium text-amber-800">{t("importantNotice")}</p>
							<p className="text-sm text-amber-700">{t("reviewNotice")}</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
