"use client";

import { UseFormReturn } from "react-hook-form";
import {
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useTranslations } from "next-intl";
import { ClinicRegistrationData } from "../clinic-registration-form";

interface BasicInfoStepProps {
	form: UseFormReturn<ClinicRegistrationData>;
}

export function BasicInfoStep({ form }: BasicInfoStepProps) {
	const t = useTranslations("clinic.registration.steps.basicInfo");

	return (
		<div className="space-y-6">
			<div className="grid grid-cols-1 gap-6 md:grid-cols-2">
				{/* Clinic Name */}
				<FormField
					control={form.control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="required">{t("name")}</FormLabel>
							<FormControl>
								<Input
									placeholder={t("namePlaceholder")}
									{...field}
								/>
							</FormControl>
							<FormDescription>
								{t("nameDescription")}
							</FormDescription>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Email */}
				<FormField
					control={form.control}
					name="email"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="required">{t("email")}</FormLabel>
							<FormControl>
								<Input
									type="email"
									placeholder={t("emailPlaceholder")}
									{...field}
								/>
							</FormControl>
							<FormDescription>
								{t("emailDescription")}
							</FormDescription>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Password */}
				<FormField
					control={form.control}
					name="password"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="required">{t("password")}</FormLabel>
							<FormControl>
								<Input
									type="password"
									placeholder="********"
									{...field}
								/>
							</FormControl>
							<FormDescription>
								{t("passwordDescription")}
							</FormDescription>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Phone */}
				<FormField
					control={form.control}
					name="phone"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="required">{t("phone")}</FormLabel>
							<FormControl>
								<Input
									type="tel"
									placeholder={t("phonePlaceholder")}
									{...field}
								/>
							</FormControl>
							<FormDescription>
								{t("phoneDescription")}
							</FormDescription>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Website */}
			<FormField
				control={form.control}
				name="website"
				render={({ field }) => (
					<FormItem>
						<FormLabel>{t("website")}</FormLabel>
						<FormControl>
							<Input
								type="url"
								placeholder={t("websitePlaceholder")}
								{...field}
							/>
						</FormControl>
						<FormDescription>
							{t("websiteDescription")}
						</FormDescription>
						<FormMessage />
					</FormItem>
				)}
			/>

			{/* Description */}
			<FormField
				control={form.control}
				name="description"
				render={({ field }) => (
					<FormItem>
						<FormLabel>{t("description")}</FormLabel>
						<FormControl>
							<Textarea
								placeholder={t("descriptionPlaceholder")}
								className="min-h-[120px]"
								{...field}
							/>
						</FormControl>
						<FormDescription>
							{t("descriptionDescription")}
						</FormDescription>
						<FormMessage />
					</FormItem>
				)}
			/>
		</div>
	);
}
