import { Resend } from "resend";
import { render } from "@react-email/render";
import { ClinicRegistrationConfirmation } from "@/components/emails/clinic-registration-confirmation";
import { ClinicVerificationStatus } from "@/components/emails/clinic-verification-status";
import { AdminClinicNotification } from "@/components/emails/admin-clinic-notification";

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY);

// Email configuration
const FROM_EMAIL = "<EMAIL>";
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || "<EMAIL>";
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000";

export interface ClinicEmailData {
	clinicId: number;
	clinicName: string;
	ownerName: string;
	ownerEmail: string;
	locale?: string;
}

export interface VerificationEmailData extends ClinicEmailData {
	status: "verified" | "rejected";
	notes?: string;
}

/**
 * Send clinic registration confirmation email
 */
export async function sendClinicRegistrationConfirmation(
	data: ClinicEmailData
): Promise<{ success: boolean; error?: string }> {
	try {
		const { clinicName, ownerName, ownerEmail, locale = "en" } = data;

		// Get subject based on locale
		const subjects = {
			en: "Clinic Registration Confirmation - Paws & Whiskers",
			fr: "Confirmation d'inscription de clinique - Paws & Whiskers", 
			ar: "تأكيد تسجيل العيادة - Paws & Whiskers"
		};

		const subject = subjects[locale as keyof typeof subjects] || subjects.en;

		// Render email template
		const emailHtml = render(
			ClinicRegistrationConfirmation({
				clinicName,
				ownerName,
				locale
			})
		);

		// Send email
		const result = await resend.emails.send({
			from: FROM_EMAIL,
			to: ownerEmail,
			subject,
			html: emailHtml,
			tags: [
				{ name: "type", value: "clinic-registration" },
				{ name: "locale", value: locale }
			]
		});

		if (result.error) {
			console.error("Failed to send clinic registration confirmation:", result.error);
			return { success: false, error: result.error.message };
		}

		console.log("Clinic registration confirmation sent:", result.data?.id);
		return { success: true };

	} catch (error) {
		console.error("Error sending clinic registration confirmation:", error);
		return { 
			success: false, 
			error: error instanceof Error ? error.message : "Unknown error" 
		};
	}
}

/**
 * Send clinic verification status email (approved/rejected)
 */
export async function sendClinicVerificationStatus(
	data: VerificationEmailData
): Promise<{ success: boolean; error?: string }> {
	try {
		const { 
			clinicId, 
			clinicName, 
			ownerName, 
			ownerEmail, 
			status, 
			notes, 
			locale = "en" 
		} = data;

		// Get subject based on status and locale
		const subjects = {
			verified: {
				en: "Clinic Verification Approved! - Paws & Whiskers",
				fr: "Vérification de clinique approuvée ! - Paws & Whiskers",
				ar: "تمت الموافقة على تحقق العيادة! - Paws & Whiskers"
			},
			rejected: {
				en: "Clinic Verification Update - Paws & Whiskers",
				fr: "Mise à jour de la vérification de clinique - Paws & Whiskers", 
				ar: "تحديث تحقق العيادة - Paws & Whiskers"
			}
		};

		const subject = subjects[status][locale as keyof typeof subjects[typeof status]] || 
						subjects[status].en;

		// Generate clinic URL for verified clinics
		const clinicUrl = status === "verified" 
			? `${SITE_URL}/${locale}/clinics/${clinicId}` 
			: undefined;

		// Render email template
		const emailHtml = render(
			ClinicVerificationStatus({
				clinicName,
				ownerName,
				status,
				notes,
				clinicUrl,
				locale
			})
		);

		// Send email
		const result = await resend.emails.send({
			from: FROM_EMAIL,
			to: ownerEmail,
			subject,
			html: emailHtml,
			tags: [
				{ name: "type", value: "clinic-verification" },
				{ name: "status", value: status },
				{ name: "locale", value: locale }
			]
		});

		if (result.error) {
			console.error("Failed to send clinic verification status:", result.error);
			return { success: false, error: result.error.message };
		}

		console.log("Clinic verification status sent:", result.data?.id);
		return { success: true };

	} catch (error) {
		console.error("Error sending clinic verification status:", error);
		return { 
			success: false, 
			error: error instanceof Error ? error.message : "Unknown error" 
		};
	}
}

/**
 * Send admin notification for new clinic registration
 */
export async function sendAdminClinicNotification(
	data: ClinicEmailData
): Promise<{ success: boolean; error?: string }> {
	try {
		const { clinicId, clinicName, ownerName, ownerEmail } = data;

		// Generate admin dashboard URL
		const adminDashboardUrl = `${SITE_URL}/admin/clinics?highlight=${clinicId}`;
		
		// Format registration date
		const registrationDate = new Date().toLocaleDateString("en-US", {
			year: "numeric",
			month: "long", 
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		});

		// Render email template
		const emailHtml = render(
			AdminClinicNotification({
				clinicName,
				ownerName,
				ownerEmail,
				adminDashboardUrl,
				clinicId,
				registrationDate
			})
		);

		// Send email to admin
		const result = await resend.emails.send({
			from: FROM_EMAIL,
			to: ADMIN_EMAIL,
			subject: `🚨 New Clinic Registration: ${clinicName}`,
			html: emailHtml,
			tags: [
				{ name: "type", value: "admin-notification" },
				{ name: "clinic-id", value: clinicId.toString() }
			]
		});

		if (result.error) {
			console.error("Failed to send admin clinic notification:", result.error);
			return { success: false, error: result.error.message };
		}

		console.log("Admin clinic notification sent:", result.data?.id);
		return { success: true };

	} catch (error) {
		console.error("Error sending admin clinic notification:", error);
		return { 
			success: false, 
			error: error instanceof Error ? error.message : "Unknown error" 
		};
	}
}

/**
 * Send all clinic registration emails (confirmation + admin notification)
 */
export async function sendClinicRegistrationEmails(
	data: ClinicEmailData
): Promise<{ success: boolean; errors: string[] }> {
	const errors: string[] = [];

	// Send confirmation to clinic owner
	const confirmationResult = await sendClinicRegistrationConfirmation(data);
	if (!confirmationResult.success && confirmationResult.error) {
		errors.push(`Confirmation email: ${confirmationResult.error}`);
	}

	// Send notification to admin
	const adminResult = await sendAdminClinicNotification(data);
	if (!adminResult.success && adminResult.error) {
		errors.push(`Admin notification: ${adminResult.error}`);
	}

	return {
		success: errors.length === 0,
		errors
	};
}
