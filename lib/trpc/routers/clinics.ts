import { z } from "zod";
import { TRPCError } from "@trpc/server";
import {
	eq,
	desc,
	asc,
	like,
	count,
	sql,
	and,
	or,
	isNull,
	isNotNull,
} from "drizzle-orm";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "../trpc";
import {
	clinicProfiles,
	clinicServices,
	clinicReviews,
	users,
} from "@/lib/db/schema";
import { generateSlug } from "@/lib/utils/slug";
import { sendClinicRegistrationEmails } from "@/lib/services/clinic-email-service";

// Input validation schemas
const listClinicsSchema = z.object({
	limit: z.number().min(1).max(50).default(20),
	offset: z.number().min(0).default(0),
	search: z.string().optional(),
	city: z.string().optional(),
	state: z.string().optional(),
	verificationStatus: z.enum(["pending", "verified", "rejected"]).optional(),
	featured: z.boolean().optional(),
	isActive: z.boolean().optional(),
	sortBy: z
		.enum(["name", "createdAt", "ratingAverage", "reviewCount"])
		.default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

const getClinicBySlugSchema = z.object({
	slug: z.string().min(1),
	includeServices: z.boolean().default(false),
	includeReviews: z.boolean().default(false),
});

const createClinicSchema = z.object({
	name: z.string().min(2).max(100),
	description: z.string().min(10).max(2000).optional(),
	phone: z.string().min(10).max(20),
	email: z.string().email(),
	address: z.string().min(5).max(200),
	city: z.string().min(2).max(50),
	state: z.string().min(2).max(50),
	postalCode: z.string().min(3).max(10).optional(),
	website: z.string().url().optional(),
	operatingHours: z
		.record(
			z.string(),
			z.object({
				open: z.string(),
				close: z.string(),
				closed: z.boolean().default(false),
			})
		)
		.optional(),
	emergencyHours: z
		.record(
			z.string(),
			z.object({
				open: z.string(),
				close: z.string(),
				available: z.boolean().default(false),
			})
		)
		.optional(),
	coordinates: z
		.object({
			lat: z.number(),
			lng: z.number(),
		})
		.optional(),
	serviceAreaRadius: z.number().min(1).max(100).optional(),
	amenities: z.array(z.string()).optional(),
	specializations: z.array(z.string()).optional(),
	socialMedia: z
		.object({
			facebook: z.string().url().optional(),
			instagram: z.string().url().optional(),
			twitter: z.string().url().optional(),
		})
		.optional(),
	images: z.array(z.string().url()).optional(),
});

const updateClinicSchema = createClinicSchema.partial().extend({
	id: z.number(),
});

const deleteClinicSchema = z.object({
	id: z.number(),
});

// Helper function to get user with role
async function getUserWithRole(ctx: any, userId: string) {
	return await ctx.db.query.users.findFirst({
		where: eq(users.id, parseInt(userId)),
		columns: {
			id: true,
			role: true,
		},
	});
}

// Helper function to generate unique clinic slug
async function generateUniqueClinicSlug(
	ctx: any,
	name: string,
	excludeId?: number
): Promise<string> {
	const baseSlug = generateSlug(name);
	let slug = baseSlug;
	let counter = 1;

	while (true) {
		const conditions = [eq(clinicProfiles.slug, slug)];
		if (excludeId) {
			conditions.push(sql`${clinicProfiles.id} != ${excludeId}`);
		}

		const existingClinic = await ctx.db.query.clinicProfiles.findFirst({
			where: and(...conditions),
		});

		if (!existingClinic) {
			return slug;
		}

		slug = `${baseSlug}-${counter}`;
		counter++;
	}
}

export const clinicsRouter = createTRPCRouter({
	// Public procedures
	list: publicProcedure
		.input(listClinicsSchema)
		.query(async ({ ctx, input }) => {
			const {
				limit,
				offset,
				search,
				city,
				state,
				verificationStatus,
				featured,
				isActive,
				sortBy,
				sortOrder,
			} = input;

			let whereConditions = [];

			// Only show active and verified clinics to public
			whereConditions.push(eq(clinicProfiles.isActive, true));
			whereConditions.push(
				eq(clinicProfiles.verificationStatus, "verified")
			);

			if (search) {
				whereConditions.push(
					or(
						like(clinicProfiles.name, `%${search}%`),
						like(clinicProfiles.description, `%${search}%`)
					)
				);
			}

			if (city) {
				whereConditions.push(like(clinicProfiles.city, `%${city}%`));
			}

			if (state) {
				whereConditions.push(like(clinicProfiles.state, `%${state}%`));
			}

			if (featured !== undefined) {
				whereConditions.push(eq(clinicProfiles.featured, featured));
			}

			const whereClause =
				whereConditions.length > 0
					? and(...whereConditions)
					: undefined;

			// Determine sort column
			let sortColumn;
			switch (sortBy) {
				case "name":
					sortColumn = clinicProfiles.name;
					break;
				case "ratingAverage":
					sortColumn = clinicProfiles.ratingAverage;
					break;
				case "reviewCount":
					sortColumn = clinicProfiles.reviewCount;
					break;
				default:
					sortColumn = clinicProfiles.createdAt;
			}

			const orderBy =
				sortOrder === "asc" ? asc(sortColumn) : desc(sortColumn);

			// Get clinics with user info
			const clinics = await ctx.db
				.select({
					id: clinicProfiles.id,
					name: clinicProfiles.name,
					slug: clinicProfiles.slug,
					description: clinicProfiles.description,
					phone: clinicProfiles.phone,
					email: clinicProfiles.email,
					address: clinicProfiles.address,
					city: clinicProfiles.city,
					state: clinicProfiles.state,
					website: clinicProfiles.website,
					images: clinicProfiles.images,
					ratingAverage: clinicProfiles.ratingAverage,
					reviewCount: clinicProfiles.reviewCount,
					featured: clinicProfiles.featured,
					createdAt: clinicProfiles.createdAt,
					// User info
					userName: users.name,
				})
				.from(clinicProfiles)
				.leftJoin(users, eq(clinicProfiles.userId, users.id))
				.where(whereClause)
				.orderBy(orderBy)
				.limit(limit)
				.offset(offset);

			// Get total count for pagination
			const totalResult = await ctx.db
				.select({ count: count() })
				.from(clinicProfiles)
				.where(whereClause);

			return {
				clinics,
				total: totalResult[0]?.count || 0,
				hasMore: offset + limit < (totalResult[0]?.count || 0),
			};
		}),

	getBySlug: publicProcedure
		.input(getClinicBySlugSchema)
		.query(async ({ ctx, input }) => {
			const { slug, includeServices, includeReviews } = input;

			// Get clinic with user info
			const clinic = await ctx.db
				.select({
					id: clinicProfiles.id,
					name: clinicProfiles.name,
					slug: clinicProfiles.slug,
					description: clinicProfiles.description,
					phone: clinicProfiles.phone,
					email: clinicProfiles.email,
					address: clinicProfiles.address,
					city: clinicProfiles.city,
					state: clinicProfiles.state,
					postalCode: clinicProfiles.postalCode,
					website: clinicProfiles.website,
					verificationStatus: clinicProfiles.verificationStatus,
					operatingHours: clinicProfiles.operatingHours,
					emergencyHours: clinicProfiles.emergencyHours,
					coordinates: clinicProfiles.coordinates,
					serviceAreaRadius: clinicProfiles.serviceAreaRadius,
					amenities: clinicProfiles.amenities,
					specializations: clinicProfiles.specializations,
					socialMedia: clinicProfiles.socialMedia,
					images: clinicProfiles.images,
					ratingAverage: clinicProfiles.ratingAverage,
					reviewCount: clinicProfiles.reviewCount,
					isActive: clinicProfiles.isActive,
					featured: clinicProfiles.featured,
					createdAt: clinicProfiles.createdAt,
					updatedAt: clinicProfiles.updatedAt,
					// User info
					userName: users.name,
					userEmail: users.email,
				})
				.from(clinicProfiles)
				.leftJoin(users, eq(clinicProfiles.userId, users.id))
				.where(eq(clinicProfiles.slug, slug))
				.then((rows) => rows[0]);

			if (!clinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			// Only show verified and active clinics to public (unless it's the owner)
			if (clinic.verificationStatus !== "verified" || !clinic.isActive) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			let services = null;
			let reviews = null;

			if (includeServices) {
				services = await ctx.db.query.clinicServices.findMany({
					where: and(
						eq(clinicServices.clinicId, clinic.id),
						eq(clinicServices.isAvailable, true)
					),
					orderBy: [asc(clinicServices.name)],
				});
			}

			if (includeReviews) {
				reviews = await ctx.db
					.select({
						id: clinicReviews.id,
						rating: clinicReviews.rating,
						title: clinicReviews.title,
						comment: clinicReviews.comment,
						createdAt: clinicReviews.createdAt,
						// User info
						userName: users.name,
					})
					.from(clinicReviews)
					.leftJoin(users, eq(clinicReviews.userId, users.id))
					.where(
						and(
							eq(clinicReviews.clinicId, clinic.id),
							eq(clinicReviews.isVisible, true),
							eq(clinicReviews.isVerified, true)
						)
					)
					.orderBy(desc(clinicReviews.createdAt))
					.limit(10);
			}

			return {
				...clinic,
				services,
				reviews,
			};
		}),

	// Protected procedures for clinic owners
	create: protectedProcedure
		.input(createClinicSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.user.id);

			if (!user || user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Only clinic users can create clinic profiles",
				});
			}

			// Check if user already has a clinic profile
			const existingClinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.userId, user.id),
			});

			if (existingClinic) {
				throw new TRPCError({
					code: "CONFLICT",
					message: "User already has a clinic profile",
				});
			}

			// Generate unique slug
			const slug = await generateUniqueClinicSlug(ctx, input.name);

			// Create clinic profile
			const newClinic = await ctx.db
				.insert(clinicProfiles)
				.values({
					userId: user.id,
					name: input.name,
					slug,
					description: input.description,
					phone: input.phone,
					email: input.email,
					address: input.address,
					city: input.city,
					state: input.state,
					postalCode: input.postalCode,
					website: input.website,
					operatingHours: input.operatingHours,
					emergencyHours: input.emergencyHours,
					coordinates: input.coordinates,
					serviceAreaRadius: input.serviceAreaRadius,
					amenities: input.amenities,
					specializations: input.specializations,
					socialMedia: input.socialMedia,
					images: input.images,
					verificationStatus: "pending",
					isActive: true,
					featured: false,
				})
				.returning();

			return newClinic[0];
		}),

	update: protectedProcedure
		.input(updateClinicSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.user.id);

			if (!user || user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Only clinic users can update clinic profiles",
				});
			}

			// Get existing clinic
			const existingClinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.id, input.id),
			});

			if (!existingClinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			// Check ownership
			if (existingClinic.userId !== user.id) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You can only update your own clinic profile",
				});
			}

			// Generate new slug if name changed
			let slug = existingClinic.slug;
			if (input.name && input.name !== existingClinic.name) {
				slug = await generateUniqueClinicSlug(
					ctx,
					input.name,
					input.id
				);
			}

			// Update clinic profile
			const updatedClinic = await ctx.db
				.update(clinicProfiles)
				.set({
					...input,
					slug,
					updatedAt: new Date(),
				})
				.where(eq(clinicProfiles.id, input.id))
				.returning();

			return updatedClinic[0];
		}),

	delete: protectedProcedure
		.input(deleteClinicSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.user.id);

			if (!user || user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Only clinic users can delete clinic profiles",
				});
			}

			// Get existing clinic
			const existingClinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.id, input.id),
			});

			if (!existingClinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			// Check ownership
			if (existingClinic.userId !== user.id) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You can only delete your own clinic profile",
				});
			}

			// Delete clinic profile (cascade will handle related records)
			await ctx.db
				.delete(clinicProfiles)
				.where(eq(clinicProfiles.id, input.id));

			return { success: true };
		}),

	// Get current user's clinic profile
	getMy: protectedProcedure.query(async ({ ctx }) => {
		const user = await getUserWithRole(ctx, ctx.user.id);

		// Allow admins to access clinic endpoints for testing purposes
		if (!user || (user.role !== "clinic" && user.role !== "admin")) {
			throw new TRPCError({
				code: "FORBIDDEN",
				message: "Only clinic users can access this endpoint",
			});
		}

		const clinic = await ctx.db.query.clinicProfiles.findFirst({
			where: eq(clinicProfiles.userId, user.id),
			with: {
				services: {
					orderBy: [asc(clinicServices.name)],
				},
			},
		});

		return clinic;
	}),

	// Create clinic profile after email verification
	createAfterVerification: protectedProcedure
		.input(createClinicSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.user.id);

			if (!user || user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Only clinic users can create clinic profiles",
				});
			}

			// Check if user already has a clinic profile
			const existingClinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.userId, user.id),
			});

			if (existingClinic) {
				throw new TRPCError({
					code: "CONFLICT",
					message: "User already has a clinic profile",
				});
			}

			// Generate unique slug
			const slug = await generateUniqueClinicSlug(ctx, input.name);

			// Create clinic profile
			const newClinic = await ctx.db
				.insert(clinicProfiles)
				.values({
					userId: user.id,
					name: input.name,
					slug,
					description: input.description,
					phone: input.phone,
					email: input.email,
					address: input.address,
					city: input.city,
					state: input.state,
					postalCode: input.postalCode,
					website: input.website,
					operatingHours: input.operatingHours,
					emergencyHours: input.emergencyHours,
					coordinates: input.coordinates,
					serviceAreaRadius: input.serviceAreaRadius,
					amenities: input.amenities,
					specializations: input.specializations,
					socialMedia: input.socialMedia,
					images: input.images,
					verificationStatus: "pending",
					isActive: true,
					featured: false,
				})
				.returning();

			// Send registration confirmation and admin notification emails
			try {
				await sendClinicRegistrationEmails({
					clinicId: newClinic[0].id,
					clinicName: input.name,
					ownerName: user.name || "Clinic Owner",
					ownerEmail: user.email,
					locale: user.locale || "en",
				});
			} catch (emailError) {
				console.error(
					"Failed to send registration emails:",
					emailError
				);
				// Don't throw error - clinic creation should still succeed
			}

			return newClinic[0];
		}),
});
