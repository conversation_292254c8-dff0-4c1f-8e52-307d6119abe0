import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { eq, desc, asc, and, count } from "drizzle-orm";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "../trpc";
import { clinicServices, clinicProfiles, users } from "@/lib/db/schema";

// Input validation schemas
const listServicesSchema = z.object({
	clinicId: z.number().optional(),
	clinicSlug: z.string().optional(),
	category: z.string().optional(),
	isAvailable: z.boolean().optional(),
	limit: z.number().min(1).max(100).default(50),
	offset: z.number().min(0).default(0),
	sortBy: z.enum(["name", "price", "duration", "createdAt"]).default("name"),
	sortOrder: z.enum(["asc", "desc"]).default("asc"),
});

const getServiceSchema = z.object({
	id: z.number(),
});

const createServiceSchema = z.object({
	clinicId: z.number(),
	name: z.string().min(2).max(100),
	description: z.string().min(10).max(1000).optional(),
	category: z.string().min(2).max(50),
	price: z.number().min(0).optional(),
	duration: z.number().min(5).max(480).optional(), // 5 minutes to 8 hours
	isAvailable: z.boolean().default(true),
	requirements: z.array(z.string()).optional(),
	notes: z.string().max(500).optional(),
});

const updateServiceSchema = createServiceSchema.partial().extend({
	id: z.number(),
});

const deleteServiceSchema = z.object({
	id: z.number(),
});

const toggleAvailabilitySchema = z.object({
	id: z.number(),
	isAvailable: z.boolean(),
});

// Helper function to get user with role
async function getUserWithRole(ctx: any, userId: string) {
	return await ctx.db.query.users.findFirst({
		where: eq(users.id, parseInt(userId)),
		columns: {
			id: true,
			role: true,
		},
	});
}

// Helper function to check clinic ownership
async function checkClinicOwnership(
	ctx: any,
	userId: number,
	clinicId: number
) {
	const clinic = await ctx.db.query.clinicProfiles.findFirst({
		where: eq(clinicProfiles.id, clinicId),
		columns: {
			id: true,
			userId: true,
		},
	});

	if (!clinic) {
		throw new TRPCError({
			code: "NOT_FOUND",
			message: "Clinic not found",
		});
	}

	if (clinic.userId !== userId) {
		throw new TRPCError({
			code: "FORBIDDEN",
			message: "You can only manage services for your own clinic",
		});
	}

	return clinic;
}

export const clinicServicesRouter = createTRPCRouter({
	// Public procedures
	list: publicProcedure
		.input(listServicesSchema)
		.query(async ({ ctx, input }) => {
			const {
				clinicId,
				clinicSlug,
				category,
				isAvailable,
				limit,
				offset,
				sortBy,
				sortOrder,
			} = input;

			let whereConditions = [];

			// Filter by clinic
			if (clinicId) {
				whereConditions.push(eq(clinicServices.clinicId, clinicId));
			} else if (clinicSlug) {
				// Join with clinic profiles to filter by slug
				const clinic = await ctx.db.query.clinicProfiles.findFirst({
					where: eq(clinicProfiles.slug, clinicSlug),
					columns: { id: true },
				});

				if (!clinic) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "Clinic not found",
					});
				}

				whereConditions.push(eq(clinicServices.clinicId, clinic.id));
			}

			// Filter by category
			if (category) {
				whereConditions.push(eq(clinicServices.category, category));
			}

			// Filter by availability (default to available for public)
			if (isAvailable !== undefined) {
				whereConditions.push(
					eq(clinicServices.isAvailable, isAvailable)
				);
			} else {
				// Default to showing only available services for public
				whereConditions.push(eq(clinicServices.isAvailable, true));
			}

			const whereClause =
				whereConditions.length > 0
					? and(...whereConditions)
					: undefined;

			// Determine sort column
			let sortColumn;
			switch (sortBy) {
				case "price":
					sortColumn = clinicServices.price;
					break;
				case "duration":
					sortColumn = clinicServices.duration;
					break;
				case "createdAt":
					sortColumn = clinicServices.createdAt;
					break;
				default:
					sortColumn = clinicServices.name;
			}

			const orderBy =
				sortOrder === "asc" ? asc(sortColumn) : desc(sortColumn);

			// Get services
			const services = await ctx.db
				.select()
				.from(clinicServices)
				.where(whereClause)
				.orderBy(orderBy)
				.limit(limit)
				.offset(offset);

			// Get total count for pagination
			const totalResult = await ctx.db
				.select({ count: count() })
				.from(clinicServices)
				.where(whereClause);

			return {
				services,
				total: totalResult[0]?.count || 0,
				hasMore: offset + limit < (totalResult[0]?.count || 0),
			};
		}),

	get: publicProcedure
		.input(getServiceSchema)
		.query(async ({ ctx, input }) => {
			const service = await ctx.db.query.clinicServices.findFirst({
				where: and(
					eq(clinicServices.id, input.id),
					eq(clinicServices.isAvailable, true)
				),
				with: {
					clinic: {
						columns: {
							id: true,
							name: true,
							slug: true,
							phone: true,
							email: true,
							address: true,
							city: true,
							state: true,
						},
					},
				},
			});

			if (!service) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Service not found",
				});
			}

			return service;
		}),

	// Protected procedures for clinic owners
	create: protectedProcedure
		.input(createServiceSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.session.user.id);

			if (!user || user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Only clinic users can create services",
				});
			}

			// Check clinic ownership
			await checkClinicOwnership(ctx, user.id, input.clinicId);

			// Create service
			const newService = await ctx.db
				.insert(clinicServices)
				.values({
					clinicId: input.clinicId,
					name: input.name,
					description: input.description,
					category: input.category,
					price: input.price,
					duration: input.duration,
					isAvailable: input.isAvailable,
					requirements: input.requirements,
					notes: input.notes,
				})
				.returning();

			return newService[0];
		}),

	update: protectedProcedure
		.input(updateServiceSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.session.user.id);

			if (!user || user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Only clinic users can update services",
				});
			}

			// Get existing service
			const existingService = await ctx.db.query.clinicServices.findFirst(
				{
					where: eq(clinicServices.id, input.id),
				}
			);

			if (!existingService) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Service not found",
				});
			}

			// Check clinic ownership
			await checkClinicOwnership(ctx, user.id, existingService.clinicId);

			// Update service
			const updatedService = await ctx.db
				.update(clinicServices)
				.set({
					...input,
					updatedAt: new Date(),
				})
				.where(eq(clinicServices.id, input.id))
				.returning();

			return updatedService[0];
		}),

	delete: protectedProcedure
		.input(deleteServiceSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.session.user.id);

			if (!user || user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Only clinic users can delete services",
				});
			}

			// Get existing service
			const existingService = await ctx.db.query.clinicServices.findFirst(
				{
					where: eq(clinicServices.id, input.id),
				}
			);

			if (!existingService) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Service not found",
				});
			}

			// Check clinic ownership
			await checkClinicOwnership(ctx, user.id, existingService.clinicId);

			// Delete service
			await ctx.db
				.delete(clinicServices)
				.where(eq(clinicServices.id, input.id));

			return { success: true };
		}),

	toggleAvailability: protectedProcedure
		.input(toggleAvailabilitySchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.session.user.id);

			if (!user || user.role !== "clinic") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message:
						"Only clinic users can toggle service availability",
				});
			}

			// Get existing service
			const existingService = await ctx.db.query.clinicServices.findFirst(
				{
					where: eq(clinicServices.id, input.id),
				}
			);

			if (!existingService) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Service not found",
				});
			}

			// Check clinic ownership
			await checkClinicOwnership(ctx, user.id, existingService.clinicId);

			// Update availability
			const updatedService = await ctx.db
				.update(clinicServices)
				.set({
					isAvailable: input.isAvailable,
					updatedAt: new Date(),
				})
				.where(eq(clinicServices.id, input.id))
				.returning();

			return updatedService[0];
		}),

	// Get services for current user's clinic
	getMy: protectedProcedure.query(async ({ ctx }) => {
		const user = await getUserWithRole(ctx, ctx.session.user.id);

		// Allow admins to access clinic endpoints for testing purposes
		if (!user || (user.role !== "clinic" && user.role !== "admin")) {
			throw new TRPCError({
				code: "FORBIDDEN",
				message: "Only clinic users can access this endpoint",
			});
		}

		// Get user's clinic
		const clinic = await ctx.db.query.clinicProfiles.findFirst({
			where: eq(clinicProfiles.userId, user.id),
			columns: { id: true },
		});

		if (!clinic) {
			throw new TRPCError({
				code: "NOT_FOUND",
				message: "Clinic profile not found",
			});
		}

		// Get all services for the clinic
		const services = await ctx.db.query.clinicServices.findMany({
			where: eq(clinicServices.clinicId, clinic.id),
			orderBy: [asc(clinicServices.name)],
		});

		return services;
	}),
});
