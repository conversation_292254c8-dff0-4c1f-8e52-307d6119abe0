import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { eq, desc, asc, and, count, avg, sql } from "drizzle-orm";
import { createTRPCRouter, publicProcedure, protectedProcedure } from "../trpc";
import { clinicReviews, clinicProfiles, users } from "@/lib/db/schema";

// Input validation schemas
const listReviewsSchema = z.object({
	clinicId: z.number().optional(),
	clinicSlug: z.string().optional(),
	rating: z.number().min(1).max(5).optional(),
	isVisible: z.boolean().optional(),
	isVerified: z.boolean().optional(),
	limit: z.number().min(1).max(50).default(20),
	offset: z.number().min(0).default(0),
	sortBy: z.enum(["rating", "createdAt", "helpful"]).default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

const getReviewSchema = z.object({
	id: z.number(),
});

const createReviewSchema = z.object({
	clinicId: z.number(),
	rating: z.number().min(1).max(5),
	title: z.string().min(5).max(100),
	comment: z.string().min(10).max(2000),
	serviceUsed: z.string().max(100).optional(),
	visitDate: z.date().optional(),
	wouldRecommend: z.boolean().default(true),
});

const updateReviewSchema = z.object({
	id: z.number(),
	rating: z.number().min(1).max(5).optional(),
	title: z.string().min(5).max(100).optional(),
	comment: z.string().min(10).max(2000).optional(),
	serviceUsed: z.string().max(100).optional(),
	visitDate: z.date().optional(),
	wouldRecommend: z.boolean().optional(),
});

const deleteReviewSchema = z.object({
	id: z.number(),
});

const moderateReviewSchema = z.object({
	id: z.number(),
	isVisible: z.boolean(),
	isVerified: z.boolean(),
	moderationNotes: z.string().max(500).optional(),
});

const markHelpfulSchema = z.object({
	id: z.number(),
	helpful: z.boolean(),
});

// Helper function to get user with role
async function getUserWithRole(ctx: any, userId: string) {
	return await ctx.db.query.users.findFirst({
		where: eq(users.id, parseInt(userId)),
		columns: {
			id: true,
			role: true,
		},
	});
}

// Helper function to update clinic rating statistics
async function updateClinicRatingStats(ctx: any, clinicId: number) {
	// Calculate average rating and review count
	const stats = await ctx.db
		.select({
			avgRating: avg(clinicReviews.rating),
			reviewCount: count(),
		})
		.from(clinicReviews)
		.where(
			and(
				eq(clinicReviews.clinicId, clinicId),
				eq(clinicReviews.isVisible, true),
				eq(clinicReviews.isVerified, true)
			)
		);

	const avgRating = stats[0]?.avgRating ? Number(stats[0].avgRating) : 0;
	const reviewCount = stats[0]?.reviewCount || 0;

	// Update clinic profile
	await ctx.db
		.update(clinicProfiles)
		.set({
			ratingAverage: avgRating,
			reviewCount: reviewCount,
			updatedAt: new Date(),
		})
		.where(eq(clinicProfiles.id, clinicId));

	return { avgRating, reviewCount };
}

export const clinicReviewsRouter = createTRPCRouter({
	// Public procedures
	list: publicProcedure
		.input(listReviewsSchema)
		.query(async ({ ctx, input }) => {
			const {
				clinicId,
				clinicSlug,
				rating,
				isVisible,
				isVerified,
				limit,
				offset,
				sortBy,
				sortOrder,
			} = input;

			let whereConditions = [];

			// Filter by clinic
			if (clinicId) {
				whereConditions.push(eq(clinicReviews.clinicId, clinicId));
			} else if (clinicSlug) {
				// Join with clinic profiles to filter by slug
				const clinic = await ctx.db.query.clinicProfiles.findFirst({
					where: eq(clinicProfiles.slug, clinicSlug),
					columns: { id: true },
				});

				if (!clinic) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "Clinic not found",
					});
				}

				whereConditions.push(eq(clinicReviews.clinicId, clinic.id));
			}

			// Filter by rating
			if (rating) {
				whereConditions.push(eq(clinicReviews.rating, rating));
			}

			// Filter by visibility (default to visible for public)
			if (isVisible !== undefined) {
				whereConditions.push(eq(clinicReviews.isVisible, isVisible));
			} else {
				// Default to showing only visible reviews for public
				whereConditions.push(eq(clinicReviews.isVisible, true));
			}

			// Filter by verification (default to verified for public)
			if (isVerified !== undefined) {
				whereConditions.push(eq(clinicReviews.isVerified, isVerified));
			} else {
				// Default to showing only verified reviews for public
				whereConditions.push(eq(clinicReviews.isVerified, true));
			}

			const whereClause =
				whereConditions.length > 0
					? and(...whereConditions)
					: undefined;

			// Determine sort column
			let sortColumn;
			switch (sortBy) {
				case "rating":
					sortColumn = clinicReviews.rating;
					break;
				case "helpful":
					sortColumn = clinicReviews.helpfulCount;
					break;
				default:
					sortColumn = clinicReviews.createdAt;
			}

			const orderBy =
				sortOrder === "asc" ? asc(sortColumn) : desc(sortColumn);

			// Get reviews with user info
			const reviews = await ctx.db
				.select({
					id: clinicReviews.id,
					rating: clinicReviews.rating,
					title: clinicReviews.title,
					comment: clinicReviews.comment,
					serviceUsed: clinicReviews.serviceUsed,
					visitDate: clinicReviews.visitDate,
					wouldRecommend: clinicReviews.wouldRecommend,
					helpfulCount: clinicReviews.helpfulCount,
					createdAt: clinicReviews.createdAt,
					// User info (anonymized for privacy)
					userName: users.name,
				})
				.from(clinicReviews)
				.leftJoin(users, eq(clinicReviews.userId, users.id))
				.where(whereClause)
				.orderBy(orderBy)
				.limit(limit)
				.offset(offset);

			// Get total count for pagination
			const totalResult = await ctx.db
				.select({ count: count() })
				.from(clinicReviews)
				.where(whereClause);

			return {
				reviews,
				total: totalResult[0]?.count || 0,
				hasMore: offset + limit < (totalResult[0]?.count || 0),
			};
		}),

	get: publicProcedure
		.input(getReviewSchema)
		.query(async ({ ctx, input }) => {
			const review = await ctx.db
				.select({
					id: clinicReviews.id,
					rating: clinicReviews.rating,
					title: clinicReviews.title,
					comment: clinicReviews.comment,
					serviceUsed: clinicReviews.serviceUsed,
					visitDate: clinicReviews.visitDate,
					wouldRecommend: clinicReviews.wouldRecommend,
					helpfulCount: clinicReviews.helpfulCount,
					isVisible: clinicReviews.isVisible,
					isVerified: clinicReviews.isVerified,
					createdAt: clinicReviews.createdAt,
					// User info
					userName: users.name,
					// Clinic info
					clinicName: clinicProfiles.name,
					clinicSlug: clinicProfiles.slug,
				})
				.from(clinicReviews)
				.leftJoin(users, eq(clinicReviews.userId, users.id))
				.leftJoin(
					clinicProfiles,
					eq(clinicReviews.clinicId, clinicProfiles.id)
				)
				.where(
					and(
						eq(clinicReviews.id, input.id),
						eq(clinicReviews.isVisible, true),
						eq(clinicReviews.isVerified, true)
					)
				)
				.then((rows) => rows[0]);

			if (!review) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Review not found",
				});
			}

			return review;
		}),

	// Protected procedures for authenticated users
	create: protectedProcedure
		.input(createReviewSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.session.user.id);

			if (!user) {
				throw new TRPCError({
					code: "UNAUTHORIZED",
					message: "Authentication required",
				});
			}

			// Check if clinic exists and is active
			const clinic = await ctx.db.query.clinicProfiles.findFirst({
				where: and(
					eq(clinicProfiles.id, input.clinicId),
					eq(clinicProfiles.isActive, true),
					eq(clinicProfiles.verificationStatus, "verified")
				),
			});

			if (!clinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found or not available for reviews",
				});
			}

			// Check if user already reviewed this clinic
			const existingReview = await ctx.db.query.clinicReviews.findFirst({
				where: and(
					eq(clinicReviews.clinicId, input.clinicId),
					eq(clinicReviews.userId, user.id)
				),
			});

			if (existingReview) {
				throw new TRPCError({
					code: "CONFLICT",
					message: "You have already reviewed this clinic",
				});
			}

			// Create review
			const newReview = await ctx.db
				.insert(clinicReviews)
				.values({
					clinicId: input.clinicId,
					userId: user.id,
					rating: input.rating,
					title: input.title,
					comment: input.comment,
					serviceUsed: input.serviceUsed,
					visitDate: input.visitDate,
					wouldRecommend: input.wouldRecommend,
					isVisible: true,
					isVerified: false, // Requires admin verification
					helpfulCount: 0,
				})
				.returning();

			// Update clinic rating stats (even for unverified reviews for now)
			await updateClinicRatingStats(ctx, input.clinicId);

			return newReview[0];
		}),

	update: protectedProcedure
		.input(updateReviewSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.session.user.id);

			if (!user) {
				throw new TRPCError({
					code: "UNAUTHORIZED",
					message: "Authentication required",
				});
			}

			// Get existing review
			const existingReview = await ctx.db.query.clinicReviews.findFirst({
				where: eq(clinicReviews.id, input.id),
			});

			if (!existingReview) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Review not found",
				});
			}

			// Check ownership
			if (existingReview.userId !== user.id) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You can only update your own reviews",
				});
			}

			// Update review (reset verification status if content changed)
			const updatedReview = await ctx.db
				.update(clinicReviews)
				.set({
					...input,
					isVerified: false, // Reset verification when updated
					updatedAt: new Date(),
				})
				.where(eq(clinicReviews.id, input.id))
				.returning();

			// Update clinic rating stats
			await updateClinicRatingStats(ctx, existingReview.clinicId);

			return updatedReview[0];
		}),

	delete: protectedProcedure
		.input(deleteReviewSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.session.user.id);

			if (!user) {
				throw new TRPCError({
					code: "UNAUTHORIZED",
					message: "Authentication required",
				});
			}

			// Get existing review
			const existingReview = await ctx.db.query.clinicReviews.findFirst({
				where: eq(clinicReviews.id, input.id),
			});

			if (!existingReview) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Review not found",
				});
			}

			// Check ownership (or admin)
			if (existingReview.userId !== user.id && user.role !== "admin") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You can only delete your own reviews",
				});
			}

			// Delete review
			await ctx.db
				.delete(clinicReviews)
				.where(eq(clinicReviews.id, input.id));

			// Update clinic rating stats
			await updateClinicRatingStats(ctx, existingReview.clinicId);

			return { success: true };
		}),

	markHelpful: protectedProcedure
		.input(markHelpfulSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.session.user.id);

			if (!user) {
				throw new TRPCError({
					code: "UNAUTHORIZED",
					message: "Authentication required",
				});
			}

			// Get existing review
			const existingReview = await ctx.db.query.clinicReviews.findFirst({
				where: eq(clinicReviews.id, input.id),
			});

			if (!existingReview) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Review not found",
				});
			}

			// Prevent users from marking their own reviews as helpful
			if (existingReview.userId === user.id) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You cannot mark your own review as helpful",
				});
			}

			// Update helpful count (simplified - in production you'd track individual votes)
			const increment = input.helpful ? 1 : -1;
			const newCount = Math.max(
				0,
				existingReview.helpfulCount + increment
			);

			const updatedReview = await ctx.db
				.update(clinicReviews)
				.set({
					helpfulCount: newCount,
					updatedAt: new Date(),
				})
				.where(eq(clinicReviews.id, input.id))
				.returning();

			return updatedReview[0];
		}),

	// Get current user's reviews
	getMy: protectedProcedure.query(async ({ ctx }) => {
		const user = await getUserWithRole(ctx, ctx.session.user.id);

		if (!user) {
			throw new TRPCError({
				code: "UNAUTHORIZED",
				message: "Authentication required",
			});
		}

		// Get all reviews by the user
		const reviews = await ctx.db
			.select({
				id: clinicReviews.id,
				rating: clinicReviews.rating,
				title: clinicReviews.title,
				comment: clinicReviews.comment,
				serviceUsed: clinicReviews.serviceUsed,
				visitDate: clinicReviews.visitDate,
				wouldRecommend: clinicReviews.wouldRecommend,
				helpfulCount: clinicReviews.helpfulCount,
				isVisible: clinicReviews.isVisible,
				isVerified: clinicReviews.isVerified,
				moderationNotes: clinicReviews.moderationNotes,
				createdAt: clinicReviews.createdAt,
				updatedAt: clinicReviews.updatedAt,
				// Clinic info
				clinicName: clinicProfiles.name,
				clinicSlug: clinicProfiles.slug,
			})
			.from(clinicReviews)
			.leftJoin(
				clinicProfiles,
				eq(clinicReviews.clinicId, clinicProfiles.id)
			)
			.where(eq(clinicReviews.userId, user.id))
			.orderBy(desc(clinicReviews.createdAt));

		return reviews;
	}),

	// Admin procedures for moderation
	moderate: protectedProcedure
		.input(moderateReviewSchema)
		.mutation(async ({ ctx, input }) => {
			const user = await getUserWithRole(ctx, ctx.session.user.id);

			if (!user || user.role !== "admin") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Only admins can moderate reviews",
				});
			}

			// Get existing review
			const existingReview = await ctx.db.query.clinicReviews.findFirst({
				where: eq(clinicReviews.id, input.id),
			});

			if (!existingReview) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Review not found",
				});
			}

			// Update review moderation status
			const updatedReview = await ctx.db
				.update(clinicReviews)
				.set({
					isVisible: input.isVisible,
					isVerified: input.isVerified,
					moderationNotes: input.moderationNotes,
					moderatedBy: user.id,
					moderatedAt: new Date(),
					updatedAt: new Date(),
				})
				.where(eq(clinicReviews.id, input.id))
				.returning();

			// Update clinic rating stats
			await updateClinicRatingStats(ctx, existingReview.clinicId);

			return updatedReview[0];
		}),

	// Get rating statistics for a clinic
	getStats: publicProcedure
		.input(
			z.object({
				clinicId: z.number().optional(),
				clinicSlug: z.string().optional(),
			})
		)
		.query(async ({ ctx, input }) => {
			let clinicId = input.clinicId;

			if (!clinicId && input.clinicSlug) {
				const clinic = await ctx.db.query.clinicProfiles.findFirst({
					where: eq(clinicProfiles.slug, input.clinicSlug),
					columns: { id: true },
				});

				if (!clinic) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "Clinic not found",
					});
				}

				clinicId = clinic.id;
			}

			if (!clinicId) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Either clinicId or clinicSlug must be provided",
				});
			}

			// Get rating distribution
			const ratingDistribution = await ctx.db
				.select({
					rating: clinicReviews.rating,
					count: count(),
				})
				.from(clinicReviews)
				.where(
					and(
						eq(clinicReviews.clinicId, clinicId),
						eq(clinicReviews.isVisible, true),
						eq(clinicReviews.isVerified, true)
					)
				)
				.groupBy(clinicReviews.rating)
				.orderBy(desc(clinicReviews.rating));

			// Get overall stats
			const overallStats = await ctx.db
				.select({
					avgRating: avg(clinicReviews.rating),
					totalReviews: count(),
				})
				.from(clinicReviews)
				.where(
					and(
						eq(clinicReviews.clinicId, clinicId),
						eq(clinicReviews.isVisible, true),
						eq(clinicReviews.isVerified, true)
					)
				);

			return {
				avgRating: overallStats[0]?.avgRating
					? Number(overallStats[0].avgRating)
					: 0,
				totalReviews: overallStats[0]?.totalReviews || 0,
				ratingDistribution: ratingDistribution.map((item) => ({
					rating: item.rating,
					count: item.count,
				})),
			};
		}),
});
