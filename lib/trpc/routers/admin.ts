import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { eq, desc, asc, like, count, sql, and, or, gte } from "drizzle-orm";
import { createTRPCRouter, protectedAdminProcedure } from "../trpc";
import {
	users,
	cats,
	messages,
	favorites,
	catImages,
	clinicProfiles,
	clinicServices,
	clinicReviews,
} from "@/lib/db/schema";
import { logSlowQuery } from "./helpers/cat-helpers";
import { sendClinicVerificationStatus } from "@/lib/services/clinic-email-service";

// Admin authorization middleware
const adminProcedure = protectedAdminProcedure.use(async ({ ctx, next }) => {
	const startTime = performance.now();
	const userId = Number(ctx?.user?.id);

	// Get user with role from database
	const user = await ctx.db.query.users.findFirst({
		where: eq(users.id, userId),
		columns: {
			id: true,
			role: true,
		},
	});

	const duration = performance.now() - startTime;
	logSlowQuery("adminAuthCheck", duration);

	if (!user || user.role !== "admin") {
		throw new TRPCError({
			code: "FORBIDDEN",
			message: "Admin access required",
		});
	}

	return next({
		ctx: {
			...ctx,
			user: {
				...ctx.user,
				role: user.role,
			},
		},
	});
});

// Input schemas
const listUsersSchema = z.object({
	limit: z.number().min(1).max(100).default(20),
	offset: z.number().min(0).default(0),
	search: z.string().optional(),
	role: z.enum(["admin", "adopter", "rescuer", "clinic"]).optional(),
	sortBy: z.enum(["name", "email", "createdAt", "role"]).default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

const listCatsSchema = z.object({
	limit: z.number().min(1).max(100).default(20),
	offset: z.number().min(0).default(0),
	search: z.string().optional(),
	status: z
		.enum(["available", "pending", "adopted", "unavailable"])
		.optional(),
	gender: z.enum(["male", "female"]).optional(),
	breedId: z.string().optional(),
	sortBy: z.enum(["name", "createdAt", "updatedAt"]).default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

const updateUserRoleSchema = z.object({
	userId: z.number(),
	role: z.enum(["admin", "adopter", "rescuer", "clinic"]),
});

const updateCatStatusSchema = z.object({
	catId: z.number(),
	status: z.enum(["available", "pending", "adopted", "unavailable"]),
});

const deleteUserSchema = z.object({
	userId: z.number(),
});

const deleteCatSchema = z.object({
	catId: z.number(),
});

export const adminRouter = createTRPCRouter({
	// Dashboard statistics
	getStats: adminProcedure.query(async ({ ctx }) => {
		const startTime = performance.now();

		const [
			totalUsers,
			totalCats,
			totalMessages,
			totalFavorites,
			usersByRole,
			catsByStatus,
			recentUsers,
			recentCats,
		] = await Promise.all([
			// Total counts
			ctx.db.select({ count: count() }).from(users),
			ctx.db.select({ count: count() }).from(cats),
			ctx.db.select({ count: count() }).from(messages),
			ctx.db.select({ count: count() }).from(favorites),

			// Users by role
			ctx.db
				.select({
					role: users.role,
					count: count(),
				})
				.from(users)
				.groupBy(users.role),

			// Cats by status
			ctx.db
				.select({
					status: cats.status,
					count: count(),
				})
				.from(cats)
				.groupBy(cats.status),

			// Recent users (last 7 days)
			ctx.db
				.select({ count: count() })
				.from(users)
				.where(gte(users.createdAt, sql`NOW() - INTERVAL '7 days'`)),

			// Recent cats (last 7 days)
			ctx.db
				.select({ count: count() })
				.from(cats)
				.where(gte(cats.createdAt, sql`NOW() - INTERVAL '7 days'`)),
		]);

		const duration = performance.now() - startTime;
		logSlowQuery("adminGetStats", duration);

		return {
			totalUsers: totalUsers[0]?.count || 0,
			totalCats: totalCats[0]?.count || 0,
			totalMessages: totalMessages[0]?.count || 0,
			totalFavorites: totalFavorites[0]?.count || 0,
			usersByRole: usersByRole.reduce(
				(acc, item) => {
					acc[item.role] = item.count;
					return acc;
				},
				{} as Record<string, number>
			),
			catsByStatus: catsByStatus.reduce(
				(acc, item) => {
					acc[item.status] = item.count;
					return acc;
				},
				{} as Record<string, number>
			),
			recentUsers: recentUsers[0]?.count || 0,
			recentCats: recentCats[0]?.count || 0,
		};
	}),

	// Adoption statistics for charts
	getAdoptionStats: adminProcedure
		.input(
			z.object({
				period: z.enum(["monthly", "weekly"]).default("monthly"),
				months: z.number().min(1).max(24).default(12),
			})
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { period, months } = input;

			if (period === "monthly") {
				// Get monthly adoption data for the last N months
				const monthlyData = await ctx.db
					.select({
						month: sql<string>`TO_CHAR(${cats.updatedAt}, 'Mon')`,
						monthNum: sql<number>`EXTRACT(MONTH FROM ${cats.updatedAt})`,
						year: sql<number>`EXTRACT(YEAR FROM ${cats.updatedAt})`,
						count: count(),
					})
					.from(cats)
					.where(
						and(
							eq(cats.status, "adopted"),
							gte(
								cats.updatedAt,
								sql`NOW() - INTERVAL '${sql.raw(months.toString())} months'`
							)
						)
					)
					.groupBy(
						sql`EXTRACT(YEAR FROM ${cats.updatedAt})`,
						sql`EXTRACT(MONTH FROM ${cats.updatedAt})`,
						sql`TO_CHAR(${cats.updatedAt}, 'Mon')`
					)
					.orderBy(
						sql`EXTRACT(YEAR FROM ${cats.updatedAt})`,
						sql`EXTRACT(MONTH FROM ${cats.updatedAt})`
					);

				const duration = performance.now() - startTime;
				logSlowQuery("adminGetAdoptionStatsMonthly", duration);

				return {
					period: "monthly" as const,
					data: monthlyData.map((item) => ({
						month: item.month,
						count: item.count,
						year: item.year,
						monthNum: item.monthNum,
					})),
				};
			} else {
				// Weekly data - get last 12 weeks
				const weeklyData = await ctx.db
					.select({
						week: sql<string>`TO_CHAR(${cats.updatedAt}, 'YYYY-"W"WW')`,
						weekStart: sql<string>`TO_CHAR(DATE_TRUNC('week', ${cats.updatedAt}), 'Mon DD')`,
						count: count(),
					})
					.from(cats)
					.where(
						and(
							eq(cats.status, "adopted"),
							gte(
								cats.updatedAt,
								sql`NOW() - INTERVAL '12 weeks'`
							)
						)
					)
					.groupBy(
						sql`DATE_TRUNC('week', ${cats.updatedAt})`,
						sql`TO_CHAR(${cats.updatedAt}, 'YYYY-"W"WW')`,
						sql`TO_CHAR(DATE_TRUNC('week', ${cats.updatedAt}), 'Mon DD')`
					)
					.orderBy(sql`DATE_TRUNC('week', ${cats.updatedAt})`);

				const duration = performance.now() - startTime;
				logSlowQuery("adminGetAdoptionStatsWeekly", duration);

				return {
					period: "weekly" as const,
					data: weeklyData.map((item) => ({
						week: item.weekStart,
						count: item.count,
						weekId: item.week,
					})),
				};
			}
		}),

	// User management
	listUsers: adminProcedure
		.input(listUsersSchema)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { limit, offset, search, role, sortBy, sortOrder } = input;

			let whereConditions = [];

			if (search) {
				whereConditions.push(
					or(
						like(users.name, `%${search}%`),
						like(users.email, `%${search}%`)
					)
				);
			}

			if (role) {
				whereConditions.push(eq(users.role, role));
			}

			const whereClause =
				whereConditions.length > 0
					? and(...whereConditions)
					: undefined;

			const orderBy =
				sortOrder === "asc" ? asc(users[sortBy]) : desc(users[sortBy]);

			const [usersList, totalCount] = await Promise.all([
				ctx.db.query.users.findMany({
					where: whereClause,
					limit,
					offset,
					orderBy,
					with: {
						wilaya: true,
						commune: true,
					},
				}),
				ctx.db
					.select({ count: count() })
					.from(users)
					.where(whereClause),
			]);

			const duration = performance.now() - startTime;
			logSlowQuery("adminListUsers", duration);

			return {
				users: usersList,
				total: totalCount[0]?.count || 0,
				limit,
				offset,
			};
		}),

	updateUserRole: adminProcedure
		.input(updateUserRoleSchema)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { userId, role } = input;

			const updatedUser = await ctx.db
				.update(users)
				.set({
					role,
					updatedAt: new Date(),
				})
				.where(eq(users.id, userId))
				.returning();

			const duration = performance.now() - startTime;
			logSlowQuery("adminUpdateUserRole", duration);

			if (!updatedUser.length) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "User not found",
				});
			}

			return updatedUser[0];
		}),

	deleteUser: adminProcedure
		.input(deleteUserSchema)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { userId } = input;

			// Check if user exists
			const user = await ctx.db.query.users.findFirst({
				where: eq(users.id, userId),
			});

			if (!user) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "User not found",
				});
			}

			// Don't allow deleting other admins
			if (user.role === "admin") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Cannot delete admin users",
				});
			}

			// Delete user (cascade will handle related records)
			await ctx.db.delete(users).where(eq(users.id, userId));

			const duration = performance.now() - startTime;
			logSlowQuery("adminDeleteUser", duration);

			return { success: true };
		}),

	// Cat management
	listCats: adminProcedure
		.input(listCatsSchema)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const {
				limit,
				offset,
				search,
				status,
				gender,
				breedId,
				sortBy,
				sortOrder,
			} = input;

			let whereConditions = [];

			if (search) {
				whereConditions.push(like(cats.name, `%${search}%`));
			}

			if (status) {
				whereConditions.push(eq(cats.status, status));
			}

			if (gender) {
				whereConditions.push(eq(cats.gender, gender));
			}

			if (breedId) {
				whereConditions.push(eq(cats.breedId, parseInt(breedId)));
			}

			const whereClause =
				whereConditions.length > 0
					? and(...whereConditions)
					: undefined;

			const orderBy =
				sortOrder === "asc" ? asc(cats[sortBy]) : desc(cats[sortBy]);

			const [catsList, totalCount] = await Promise.all([
				ctx.db.query.cats.findMany({
					where: whereClause,
					limit,
					offset,
					orderBy,
					with: {
						user: {
							columns: {
								id: true,
								name: true,
								email: true,
								role: true,
								slug: true,
							},
						},
						images: {
							limit: 1,
							orderBy: asc(catImages.createdAt),
						},
						wilaya: true,
						commune: true,
					},
				}),
				ctx.db.select({ count: count() }).from(cats).where(whereClause),
			]);

			const duration = performance.now() - startTime;
			logSlowQuery("adminListCats", duration);

			return {
				cats: catsList,
				total: totalCount[0]?.count || 0,
				limit,
				offset,
			};
		}),

	updateCatStatus: adminProcedure
		.input(updateCatStatusSchema)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { catId, status } = input;

			const updatedCat = await ctx.db
				.update(cats)
				.set({
					status,
					updatedAt: new Date(),
				})
				.where(eq(cats.id, catId))
				.returning();

			const duration = performance.now() - startTime;
			logSlowQuery("adminUpdateCatStatus", duration);

			if (!updatedCat.length) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			return updatedCat[0];
		}),

	deleteCat: adminProcedure
		.input(deleteCatSchema)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { catId } = input;

			// Check if cat exists
			const cat = await ctx.db.query.cats.findFirst({
				where: eq(cats.id, catId),
			});

			if (!cat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			// Delete cat (cascade will handle related records)
			await ctx.db.delete(cats).where(eq(cats.id, catId));

			const duration = performance.now() - startTime;
			logSlowQuery("adminDeleteCat", duration);

			return { success: true };
		}),

	// Clinic management procedures
	listClinics: adminProcedure
		.input(
			z.object({
				limit: z.number().min(1).max(100).default(20),
				offset: z.number().min(0).default(0),
				search: z.string().optional(),
				verificationStatus: z
					.enum(["pending", "verified", "rejected"])
					.optional(),
				isActive: z.boolean().optional(),
				city: z.string().optional(),
				state: z.string().optional(),
				sortBy: z
					.enum([
						"name",
						"createdAt",
						"verificationStatus",
						"ratingAverage",
					])
					.default("createdAt"),
				sortOrder: z.enum(["asc", "desc"]).default("desc"),
			})
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const {
				limit,
				offset,
				search,
				verificationStatus,
				isActive,
				city,
				state,
				sortBy,
				sortOrder,
			} = input;

			let whereConditions = [];

			if (search) {
				whereConditions.push(
					or(
						like(clinicProfiles.name, `%${search}%`),
						like(clinicProfiles.email, `%${search}%`),
						like(clinicProfiles.phone, `%${search}%`)
					)
				);
			}

			if (verificationStatus) {
				whereConditions.push(
					eq(clinicProfiles.verificationStatus, verificationStatus)
				);
			}

			if (isActive !== undefined) {
				whereConditions.push(eq(clinicProfiles.isActive, isActive));
			}

			if (city) {
				whereConditions.push(like(clinicProfiles.city, `%${city}%`));
			}

			if (state) {
				whereConditions.push(like(clinicProfiles.state, `%${state}%`));
			}

			const whereClause =
				whereConditions.length > 0
					? and(...whereConditions)
					: undefined;

			// Determine sort column
			let sortColumn;
			switch (sortBy) {
				case "name":
					sortColumn = clinicProfiles.name;
					break;
				case "verificationStatus":
					sortColumn = clinicProfiles.verificationStatus;
					break;
				case "ratingAverage":
					sortColumn = clinicProfiles.ratingAverage;
					break;
				default:
					sortColumn = clinicProfiles.createdAt;
			}

			const orderBy =
				sortOrder === "asc" ? asc(sortColumn) : desc(sortColumn);

			// Get clinics with user info
			const clinics = await ctx.db
				.select({
					id: clinicProfiles.id,
					name: clinicProfiles.name,
					slug: clinicProfiles.slug,
					email: clinicProfiles.email,
					phone: clinicProfiles.phone,
					address: clinicProfiles.address,
					city: clinicProfiles.city,
					state: clinicProfiles.state,
					verificationStatus: clinicProfiles.verificationStatus,
					isActive: clinicProfiles.isActive,
					featured: clinicProfiles.featured,
					ratingAverage: clinicProfiles.ratingAverage,
					reviewCount: clinicProfiles.reviewCount,
					createdAt: clinicProfiles.createdAt,
					updatedAt: clinicProfiles.updatedAt,
					// User info
					userId: users.id,
					userName: users.name,
					userEmail: users.email,
				})
				.from(clinicProfiles)
				.leftJoin(users, eq(clinicProfiles.userId, users.id))
				.where(whereClause)
				.orderBy(orderBy)
				.limit(limit)
				.offset(offset);

			// Get total count for pagination
			const totalResult = await ctx.db
				.select({ count: count() })
				.from(clinicProfiles)
				.where(whereClause);

			const duration = performance.now() - startTime;
			logSlowQuery("adminListClinics", duration);

			return {
				clinics,
				total: totalResult[0]?.count || 0,
				hasMore: offset + limit < (totalResult[0]?.count || 0),
			};
		}),

	getClinic: adminProcedure
		.input(
			z.object({
				id: z.number(),
			})
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();

			const clinic = await ctx.db
				.select({
					id: clinicProfiles.id,
					name: clinicProfiles.name,
					slug: clinicProfiles.slug,
					description: clinicProfiles.description,
					email: clinicProfiles.email,
					phone: clinicProfiles.phone,
					address: clinicProfiles.address,
					city: clinicProfiles.city,
					state: clinicProfiles.state,
					postalCode: clinicProfiles.postalCode,
					website: clinicProfiles.website,
					verificationStatus: clinicProfiles.verificationStatus,
					operatingHours: clinicProfiles.operatingHours,
					emergencyHours: clinicProfiles.emergencyHours,
					coordinates: clinicProfiles.coordinates,
					serviceAreaRadius: clinicProfiles.serviceAreaRadius,
					amenities: clinicProfiles.amenities,
					specializations: clinicProfiles.specializations,
					socialMedia: clinicProfiles.socialMedia,
					images: clinicProfiles.images,
					isActive: clinicProfiles.isActive,
					featured: clinicProfiles.featured,
					ratingAverage: clinicProfiles.ratingAverage,
					reviewCount: clinicProfiles.reviewCount,
					createdAt: clinicProfiles.createdAt,
					updatedAt: clinicProfiles.updatedAt,
					// User info
					userId: users.id,
					userName: users.name,
					userEmail: users.email,
					userRole: users.role,
				})
				.from(clinicProfiles)
				.leftJoin(users, eq(clinicProfiles.userId, users.id))
				.where(eq(clinicProfiles.id, input.id))
				.then((rows) => rows[0]);

			if (!clinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			// Get services count
			const servicesCount = await ctx.db
				.select({ count: count() })
				.from(clinicServices)
				.where(eq(clinicServices.clinicId, input.id));

			// Get reviews count by status
			const reviewsStats = await ctx.db
				.select({
					total: count(),
					verified: sql<number>`COUNT(CASE WHEN ${clinicReviews.isVerified} = true THEN 1 END)`,
					pending: sql<number>`COUNT(CASE WHEN ${clinicReviews.isVerified} = false THEN 1 END)`,
				})
				.from(clinicReviews)
				.where(eq(clinicReviews.clinicId, input.id));

			const duration = performance.now() - startTime;
			logSlowQuery("adminGetClinic", duration);

			return {
				...clinic,
				servicesCount: servicesCount[0]?.count || 0,
				reviewsStats: {
					total: reviewsStats[0]?.total || 0,
					verified: reviewsStats[0]?.verified || 0,
					pending: reviewsStats[0]?.pending || 0,
				},
			};
		}),

	verifyClinic: adminProcedure
		.input(
			z.object({
				id: z.number(),
				verificationStatus: z.enum(["pending", "verified", "rejected"]),
				notes: z.string().max(1000).optional(),
			})
		)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();

			// Check if clinic exists with user information
			const existingClinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.id, input.id),
				with: {
					user: true,
				},
			});

			if (!existingClinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			// Update verification status
			const updatedClinic = await ctx.db
				.update(clinicProfiles)
				.set({
					verificationStatus: input.verificationStatus,
					verificationNotes: input.notes,
					verifiedAt:
						input.verificationStatus === "verified"
							? new Date()
							: null,
					updatedAt: new Date(),
				})
				.where(eq(clinicProfiles.id, input.id))
				.returning();

			// Send email notification if status changed to verified or rejected
			if (input.verificationStatus !== "pending") {
				try {
					await sendClinicVerificationStatus({
						clinicId: input.id,
						clinicName: existingClinic.name,
						ownerName: existingClinic.user.name || "Clinic Owner",
						ownerEmail: existingClinic.user.email,
						status: input.verificationStatus as
							| "verified"
							| "rejected",
						notes: input.notes,
						locale: existingClinic.user.locale || "en",
					});
				} catch (emailError) {
					console.error(
						"Failed to send verification email:",
						emailError
					);
					// Don't throw error - clinic verification should still succeed
				}
			}

			const duration = performance.now() - startTime;
			logSlowQuery("adminVerifyClinic", duration);

			return updatedClinic[0];
		}),

	toggleClinicStatus: adminProcedure
		.input(
			z.object({
				id: z.number(),
				isActive: z.boolean(),
			})
		)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();

			// Check if clinic exists
			const existingClinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.id, input.id),
			});

			if (!existingClinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			// Update active status
			const updatedClinic = await ctx.db
				.update(clinicProfiles)
				.set({
					isActive: input.isActive,
					updatedAt: new Date(),
				})
				.where(eq(clinicProfiles.id, input.id))
				.returning();

			const duration = performance.now() - startTime;
			logSlowQuery("adminToggleClinicStatus", duration);

			return updatedClinic[0];
		}),

	toggleClinicFeatured: adminProcedure
		.input(
			z.object({
				id: z.number(),
				featured: z.boolean(),
			})
		)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();

			// Check if clinic exists
			const existingClinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.id, input.id),
			});

			if (!existingClinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			// Update featured status
			const updatedClinic = await ctx.db
				.update(clinicProfiles)
				.set({
					featured: input.featured,
					updatedAt: new Date(),
				})
				.where(eq(clinicProfiles.id, input.id))
				.returning();

			const duration = performance.now() - startTime;
			logSlowQuery("adminToggleClinicFeatured", duration);

			return updatedClinic[0];
		}),

	deleteClinic: adminProcedure
		.input(
			z.object({
				id: z.number(),
			})
		)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();

			// Check if clinic exists
			const existingClinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.id, input.id),
			});

			if (!existingClinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			// Delete clinic (cascade will handle related records)
			await ctx.db
				.delete(clinicProfiles)
				.where(eq(clinicProfiles.id, input.id));

			const duration = performance.now() - startTime;
			logSlowQuery("adminDeleteClinic", duration);

			return { success: true };
		}),

	// Clinic statistics for admin dashboard
	getClinicStats: adminProcedure.query(async ({ ctx }) => {
		const startTime = performance.now();

		// Get clinic counts by status
		const statusStats = await ctx.db
			.select({
				verificationStatus: clinicProfiles.verificationStatus,
				count: count(),
			})
			.from(clinicProfiles)
			.groupBy(clinicProfiles.verificationStatus);

		// Get total clinics
		const totalClinics = await ctx.db
			.select({ count: count() })
			.from(clinicProfiles);

		// Get active clinics
		const activeClinics = await ctx.db
			.select({ count: count() })
			.from(clinicProfiles)
			.where(eq(clinicProfiles.isActive, true));

		// Get featured clinics
		const featuredClinics = await ctx.db
			.select({ count: count() })
			.from(clinicProfiles)
			.where(eq(clinicProfiles.featured, true));

		// Get recent clinics (last 30 days)
		const thirtyDaysAgo = new Date();
		thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

		const recentClinics = await ctx.db
			.select({ count: count() })
			.from(clinicProfiles)
			.where(gte(clinicProfiles.createdAt, thirtyDaysAgo));

		const duration = performance.now() - startTime;
		logSlowQuery("adminGetClinicStats", duration);

		return {
			total: totalClinics[0]?.count || 0,
			active: activeClinics[0]?.count || 0,
			featured: featuredClinics[0]?.count || 0,
			recent: recentClinics[0]?.count || 0,
			byStatus: statusStats.reduce(
				(acc, stat) => {
					acc[stat.verificationStatus] = stat.count;
					return acc;
				},
				{} as Record<string, number>
			),
		};
	}),
});
