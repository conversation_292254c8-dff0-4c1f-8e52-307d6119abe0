import { z } from "zod";
import { SERVICE_CATEGORIES } from "@/components/clinic/service-form";

// Base validation schemas for clinic-related data

// Clinic Profile Schemas
export const clinicProfileBaseSchema = z.object({
	name: z
		.string()
		.min(2, "Name must be at least 2 characters")
		.max(100, "Name must be less than 100 characters"),
	description: z
		.string()
		.min(10, "Description must be at least 10 characters")
		.max(2000, "Description must be less than 2000 characters")
		.optional(),
	phone: z
		.string()
		.min(10, "Phone must be at least 10 characters")
		.max(20, "Phone must be less than 20 characters")
		.regex(/^[\d\s\-\+\(\)]+$/, "Invalid phone number format"),
	email: z.string().email("Invalid email format"),
	address: z
		.string()
		.min(5, "Address must be at least 5 characters")
		.max(200, "Address must be less than 200 characters"),
	city: z
		.string()
		.min(2, "City must be at least 2 characters")
		.max(50, "City must be less than 50 characters"),
	state: z
		.string()
		.min(2, "State must be at least 2 characters")
		.max(50, "State must be less than 50 characters"),
	postalCode: z
		.string()
		.min(3, "Postal code must be at least 3 characters")
		.max(10, "Postal code must be less than 10 characters")
		.optional(),
	website: z.string().url("Invalid website URL").optional(),
});

export const clinicOperatingHoursSchema = z
	.record(
		z.enum([
			"monday",
			"tuesday",
			"wednesday",
			"thursday",
			"friday",
			"saturday",
			"sunday",
		]),
		z.object({
			open: z
				.string()
				.regex(
					/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
					"Invalid time format (HH:MM)"
				),
			close: z
				.string()
				.regex(
					/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
					"Invalid time format (HH:MM)"
				),
			closed: z.boolean().default(false),
		})
	)
	.optional();

export const clinicEmergencyHoursSchema = z
	.record(
		z.enum([
			"monday",
			"tuesday",
			"wednesday",
			"thursday",
			"friday",
			"saturday",
			"sunday",
		]),
		z.object({
			open: z
				.string()
				.regex(
					/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
					"Invalid time format (HH:MM)"
				),
			close: z
				.string()
				.regex(
					/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
					"Invalid time format (HH:MM)"
				),
			available: z.boolean().default(false),
		})
	)
	.optional();

export const clinicCoordinatesSchema = z
	.object({
		lat: z
			.number()
			.min(-90, "Latitude must be between -90 and 90")
			.max(90, "Latitude must be between -90 and 90"),
		lng: z
			.number()
			.min(-180, "Longitude must be between -180 and 180")
			.max(180, "Longitude must be between -180 and 180"),
	})
	.optional();

export const clinicSocialMediaSchema = z
	.object({
		facebook: z.string().url("Invalid Facebook URL").optional(),
		instagram: z.string().url("Invalid Instagram URL").optional(),
		twitter: z.string().url("Invalid Twitter URL").optional(),
	})
	.optional();

export const clinicAmenitiesSchema = z
	.array(
		z
			.string()
			.min(1, "Amenity name cannot be empty")
			.max(50, "Amenity name must be less than 50 characters")
	)
	.max(20, "Maximum 20 amenities allowed")
	.optional();

export const clinicSpecializationsSchema = z
	.array(
		z
			.string()
			.min(1, "Specialization cannot be empty")
			.max(50, "Specialization must be less than 50 characters")
	)
	.max(15, "Maximum 15 specializations allowed")
	.optional();

export const clinicImagesSchema = z
	.array(z.string().url("Invalid image URL"))
	.max(10, "Maximum 10 images allowed")
	.optional();

// Complete clinic profile schema
export const createClinicProfileSchema = clinicProfileBaseSchema.extend({
	operatingHours: clinicOperatingHoursSchema,
	emergencyHours: clinicEmergencyHoursSchema,
	coordinates: clinicCoordinatesSchema,
	serviceAreaRadius: z
		.number()
		.min(1, "Service area radius must be at least 1 km")
		.max(100, "Service area radius must be less than 100 km")
		.optional(),
	amenities: clinicAmenitiesSchema,
	specializations: clinicSpecializationsSchema,
	socialMedia: clinicSocialMediaSchema,
	images: clinicImagesSchema,
});

export const updateClinicProfileSchema = createClinicProfileSchema
	.partial()
	.extend({
		id: z.number().positive("Invalid clinic ID"),
	});

// Clinic Service Schemas
export const clinicServiceCategorySchema = z.enum(SERVICE_CATEGORIES, {
	message: "Invalid service category",
});

export const createClinicServiceSchema = z.object({
	clinicId: z.number().positive("Invalid clinic ID"),
	name: z
		.string()
		.min(2, "Service name must be at least 2 characters")
		.max(100, "Service name must be less than 100 characters"),
	description: z
		.string()
		.min(10, "Description must be at least 10 characters")
		.max(1000, "Description must be less than 1000 characters")
		.optional(),
	category: clinicServiceCategorySchema,
	price: z
		.number()
		.min(0, "Price cannot be negative")
		.max(10000, "Price must be reasonable")
		.optional(),
	duration: z
		.number()
		.min(5, "Duration must be at least 5 minutes")
		.max(480, "Duration cannot exceed 8 hours")
		.optional(),
	isAvailable: z.boolean().default(true),
	requirements: z
		.array(
			z
				.string()
				.min(1, "Requirement cannot be empty")
				.max(100, "Requirement must be less than 100 characters")
		)
		.max(10, "Maximum 10 requirements allowed")
		.optional(),
	notes: z
		.string()
		.max(500, "Notes must be less than 500 characters")
		.optional(),
});

export const updateClinicServiceSchema = createClinicServiceSchema
	.partial()
	.extend({
		id: z.number().positive("Invalid service ID"),
	});

// Clinic Review Schemas
export const clinicReviewRatingSchema = z
	.number()
	.min(1, "Rating must be at least 1")
	.max(5, "Rating cannot exceed 5")
	.int("Rating must be a whole number");

export const createClinicReviewSchema = z.object({
	clinicId: z.number().positive("Invalid clinic ID"),
	rating: clinicReviewRatingSchema,
	title: z
		.string()
		.min(5, "Title must be at least 5 characters")
		.max(100, "Title must be less than 100 characters"),
	comment: z
		.string()
		.min(10, "Comment must be at least 10 characters")
		.max(2000, "Comment must be less than 2000 characters"),
	serviceUsed: z
		.string()
		.max(100, "Service name must be less than 100 characters")
		.optional(),
	visitDate: z
		.date()
		.max(new Date(), "Visit date cannot be in the future")
		.optional(),
	wouldRecommend: z.boolean().default(true),
});

export const updateClinicReviewSchema = z.object({
	id: z.number().positive("Invalid review ID"),
	rating: clinicReviewRatingSchema.optional(),
	title: z
		.string()
		.min(5, "Title must be at least 5 characters")
		.max(100, "Title must be less than 100 characters")
		.optional(),
	comment: z
		.string()
		.min(10, "Comment must be at least 10 characters")
		.max(2000, "Comment must be less than 2000 characters")
		.optional(),
	serviceUsed: z
		.string()
		.max(100, "Service name must be less than 100 characters")
		.optional(),
	visitDate: z
		.date()
		.max(new Date(), "Visit date cannot be in the future")
		.optional(),
	wouldRecommend: z.boolean().optional(),
});

// Admin Schemas
export const verificationStatusSchema = z.enum(
	["pending", "verified", "rejected"],
	{
		errorMap: () => ({ message: "Invalid verification status" }),
	}
);

export const adminVerifyClinicSchema = z.object({
	id: z.number().positive("Invalid clinic ID"),
	verificationStatus: verificationStatusSchema,
	notes: z
		.string()
		.max(1000, "Notes must be less than 1000 characters")
		.optional(),
});

export const adminModerateReviewSchema = z.object({
	id: z.number().positive("Invalid review ID"),
	isVisible: z.boolean(),
	isVerified: z.boolean(),
	moderationNotes: z
		.string()
		.max(500, "Moderation notes must be less than 500 characters")
		.optional(),
});

// Query Schemas
export const listClinicsQuerySchema = z.object({
	limit: z
		.number()
		.min(1, "Limit must be at least 1")
		.max(50, "Limit cannot exceed 50")
		.default(20),
	offset: z.number().min(0, "Offset cannot be negative").default(0),
	search: z
		.string()
		.max(100, "Search term must be less than 100 characters")
		.optional(),
	city: z
		.string()
		.max(50, "City name must be less than 50 characters")
		.optional(),
	state: z
		.string()
		.max(50, "State name must be less than 50 characters")
		.optional(),
	verificationStatus: verificationStatusSchema.optional(),
	featured: z.boolean().optional(),
	isActive: z.boolean().optional(),
	sortBy: z
		.enum(["name", "createdAt", "ratingAverage", "reviewCount"])
		.default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const listServicesQuerySchema = z.object({
	clinicId: z.number().positive("Invalid clinic ID").optional(),
	clinicSlug: z.string().min(1, "Clinic slug cannot be empty").optional(),
	category: clinicServiceCategorySchema.optional(),
	isAvailable: z.boolean().optional(),
	limit: z
		.number()
		.min(1, "Limit must be at least 1")
		.max(100, "Limit cannot exceed 100")
		.default(50),
	offset: z.number().min(0, "Offset cannot be negative").default(0),
	sortBy: z.enum(["name", "price", "duration", "createdAt"]).default("name"),
	sortOrder: z.enum(["asc", "desc"]).default("asc"),
});

export const listReviewsQuerySchema = z.object({
	clinicId: z.number().positive("Invalid clinic ID").optional(),
	clinicSlug: z.string().min(1, "Clinic slug cannot be empty").optional(),
	rating: clinicReviewRatingSchema.optional(),
	isVisible: z.boolean().optional(),
	isVerified: z.boolean().optional(),
	limit: z
		.number()
		.min(1, "Limit must be at least 1")
		.max(50, "Limit cannot exceed 50")
		.default(20),
	offset: z.number().min(0, "Offset cannot be negative").default(0),
	sortBy: z.enum(["rating", "createdAt", "helpful"]).default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

// ID Schemas
export const clinicIdSchema = z.object({
	id: z.number().positive("Invalid clinic ID"),
});

export const serviceIdSchema = z.object({
	id: z.number().positive("Invalid service ID"),
});

export const reviewIdSchema = z.object({
	id: z.number().positive("Invalid review ID"),
});

export const clinicSlugSchema = z.object({
	slug: z
		.string()
		.min(1, "Clinic slug cannot be empty")
		.max(100, "Clinic slug must be less than 100 characters"),
	includeServices: z.boolean().default(false),
	includeReviews: z.boolean().default(false),
});

// Utility Schemas
export const toggleBooleanSchema = z.object({
	id: z.number().positive("Invalid ID"),
	value: z.boolean(),
});

export const markHelpfulSchema = z.object({
	id: z.number().positive("Invalid review ID"),
	helpful: z.boolean(),
});

// Export all schemas as a collection for easy importing
export const clinicSchemas = {
	// Profile schemas
	createClinicProfile: createClinicProfileSchema,
	updateClinicProfile: updateClinicProfileSchema,

	// Service schemas
	createClinicService: createClinicServiceSchema,
	updateClinicService: updateClinicServiceSchema,

	// Review schemas
	createClinicReview: createClinicReviewSchema,
	updateClinicReview: updateClinicReviewSchema,

	// Admin schemas
	adminVerifyClinic: adminVerifyClinicSchema,
	adminModerateReview: adminModerateReviewSchema,

	// Query schemas
	listClinics: listClinicsQuerySchema,
	listServices: listServicesQuerySchema,
	listReviews: listReviewsQuerySchema,

	// ID schemas
	clinicId: clinicIdSchema,
	serviceId: serviceIdSchema,
	reviewId: reviewIdSchema,
	clinicSlug: clinicSlugSchema,

	// Utility schemas
	toggleBoolean: toggleBooleanSchema,
	markHelpful: markHelpfulSchema,
} as const;
