import { relations } from "drizzle-orm";
import {
	pgTable,
	serial,
	text,
	timestamp,
	boolean,
	integer,
	pgEnum,
	index,
	jsonb,
	decimal,
	point,
} from "drizzle-orm/pg-core";

// Enums
export const userRoleEnum = pgEnum("user_role", [
	"admin",
	"adopter",
	"rescuer",
	"clinic",
]);
export const catGenderEnum = pgEnum("cat_gender", ["male", "female"]);
export const messageStatusEnum = pgEnum("message_status", [
	"sent",
	"delivered",
	"read",
]);
export const adoptionStatusEnum = pgEnum("adoption_status", [
	"pending",
	"approved",
	"rejected",
]);
export const catStatusEnum = pgEnum("cat_status", [
	"available",
	"pending",
	"adopted",
	"unavailable",
]);
export const verificationStatusEnum = pgEnum("verification_status", [
	"pending",
	"verified",
	"rejected",
]);

// Cat breeds table
export const catBreeds = pgTable(
	"cat_breeds",
	{
		id: serial("id").primaryKey(),
		name: text("name").notNull().unique(),
		description: text("description"),
		origin: text("origin"),
		temperament: text("temperament"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => [
		// Search performance index
		index("cat_breeds_name_idx").on(table.name),
	]
);

// Algerian wilayas (states) table
export const wilayas = pgTable(
	"wilayas",
	{
		id: serial("id").primaryKey(),
		code: text("code").notNull().unique(),
		name: text("name").notNull().unique(),
		nameAr: text("name_ar"),
		nameFr: text("name_fr"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => [
		// Search performance indexes
		index("wilayas_name_idx").on(table.name),
		index("wilayas_name_ar_idx").on(table.nameAr),
		index("wilayas_name_fr_idx").on(table.nameFr),
	]
);

// Algerian communes table
export const communes = pgTable(
	"communes",
	{
		id: serial("id").primaryKey(),
		name: text("name").notNull(),
		nameAr: text("name_ar"),
		nameFr: text("name_fr"),
		wilayaId: integer("wilaya_id")
			.notNull()
			.references(() => wilayas.id),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => [
		// Search performance indexes
		index("communes_name_idx").on(table.name),
		index("communes_name_ar_idx").on(table.nameAr),
		index("communes_name_fr_idx").on(table.nameFr),
		// Foreign key index
		index("communes_wilaya_id_idx").on(table.wilayaId),
	]
);

// Users table
export const users = pgTable(
	"users",
	{
		id: serial("id").primaryKey(),
		name: text("name").notNull(),
		email: text("email").notNull().unique(),
		slug: text("slug").notNull().unique(),
		role: userRoleEnum("role").notNull().default("adopter"),
		bio: text("bio"),
		location: text("location"), // Keep for backward compatibility
		wilayaId: integer("wilaya_id").references(() => wilayas.id),
		communeId: integer("commune_id").references(() => communes.id),
		phone: text("phone"),
		image: text("image"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
		emailVerified: boolean("email_verified").default(false).notNull(),
	},
	(table) => [
		// Search performance index
		index("users_name_idx").on(table.name),
		// Slug index for fast lookups
		index("users_slug_idx").on(table.slug),
		// Location indexes for performance
		index("users_wilaya_id_idx").on(table.wilayaId),
		index("users_commune_id_idx").on(table.communeId),
	]
);

// Sessions table
export const sessions = pgTable("sessions", {
	id: serial("id").primaryKey(),
	expiresAt: timestamp("expires_at").notNull(),
	token: text("token").notNull().unique(),
	createdAt: timestamp("created_at").notNull(),
	updatedAt: timestamp("updated_at").notNull(),
	ipAddress: text("ip_address"),
	userAgent: text("user_agent"),
	userId: integer("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
});

// Accounts table
export const accounts = pgTable("accounts", {
	id: serial("id").primaryKey(),
	accountId: text("account_id").notNull(),
	providerId: text("provider_id").notNull(),
	userId: integer("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	accessToken: text("access_token"),
	refreshToken: text("refresh_token"),
	idToken: text("id_token"),
	accessTokenExpiresAt: timestamp("access_token_expires_at"),
	refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
	scope: text("scope"),
	password: text("password"),
	createdAt: timestamp("created_at").notNull(),
	updatedAt: timestamp("updated_at").notNull(),
});

// Verifications table
export const verifications = pgTable("verifications", {
	id: serial("id").primaryKey(),
	identifier: text("identifier").notNull(),
	value: text("value").notNull(),
	expiresAt: timestamp("expires_at").notNull(),
	createdAt: timestamp("created_at").$defaultFn(
		() => /* @__PURE__ */ new Date()
	),
	updatedAt: timestamp("updated_at").$defaultFn(
		() => /* @__PURE__ */ new Date()
	),
});

// Cats table
export const cats = pgTable(
	"cats",
	{
		id: serial("id").primaryKey(),
		name: text("name").notNull(),
		slug: text("slug").notNull().unique(),
		gender: catGenderEnum("gender").notNull(),
		age: integer("age").notNull(),
		breedId: integer("breed_id").references(() => catBreeds.id),
		description: text("description").notNull(),
		story: text("story"),
		wilayaId: integer("wilaya_id").references(() => wilayas.id),
		communeId: integer("commune_id").references(() => communes.id),
		vaccinated: boolean("vaccinated").default(false),
		neutered: boolean("neutered").default(false),
		specialNeeds: boolean("special_needs").default(false),
		specialNeedsDescription: text("special_needs_description"),
		adopted: boolean("adopted").default(false),
		status: catStatusEnum("status").default("available").notNull(),
		isDraft: boolean("is_draft").default(true),
		featured: boolean("featured").default(false),
		userId: integer("user_id")
			.notNull()
			.references(() => users.id),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => [
		// Search performance indexes
		index("cats_name_idx").on(table.name),
		index("cats_description_idx").on(table.description),
		index("cats_story_idx").on(table.story),
		// Slug index for fast lookups
		index("cats_slug_idx").on(table.slug),
		index("cats_special_needs_desc_idx").on(table.specialNeedsDescription),
		// Foreign key indexes for JOIN performance
		index("cats_breed_id_idx").on(table.breedId),
		index("cats_wilaya_id_idx").on(table.wilayaId),
		index("cats_commune_id_idx").on(table.communeId),
		index("cats_user_id_idx").on(table.userId),
		// Critical filtering indexes for performance optimization
		index("cats_is_draft_idx").on(table.isDraft), // Highest priority - used in every query
		index("cats_adopted_idx").on(table.adopted), // Used in notAdopted filter
		index("cats_created_at_idx").on(table.createdAt), // Used for default sorting
		index("cats_status_idx").on(table.status), // Used for status filtering
		index("cats_featured_idx").on(table.featured), // Used in getFeatured query
		index("cats_gender_idx").on(table.gender), // Used in gender filtering
		index("cats_vaccinated_idx").on(table.vaccinated), // Used in vaccinated filter
		index("cats_neutered_idx").on(table.neutered), // Used in neutered filter
		index("cats_special_needs_idx").on(table.specialNeeds), // Used in specialNeeds filter
		// Composite indexes for common filter combinations (Phase 1.2)
		index("cats_is_draft_adopted_idx").on(table.isDraft, table.adopted), // Most common combination
		index("cats_is_draft_created_at_idx").on(
			table.isDraft,
			table.createdAt
		), // For sorting with draft exclusion
		index("cats_is_draft_status_idx").on(table.isDraft, table.status), // For status-based filtering
		index("cats_is_draft_featured_idx").on(table.isDraft, table.featured), // For featured cats query
		// Age indexes for numeric filtering (Phase 1.3)
		index("cats_age_idx").on(table.age), // For age range filtering
		index("cats_is_draft_age_idx").on(table.isDraft, table.age), // For age range filtering with draft exclusion
	]
);

// Cat images table
export const catImages = pgTable(
	"cat_images",
	{
		id: serial("id").primaryKey(),
		catId: integer("cat_id")
			.notNull()
			.references(() => cats.id),
		url: text("url").notNull(),
		isPrimary: boolean("is_primary").default(false),
		createdAt: timestamp("created_at").defaultNow().notNull(),
	},
	(table) => [
		// Performance indexes for cat image queries
		index("cat_images_cat_id_idx").on(table.catId), // For finding images by cat
		index("cat_images_is_primary_idx").on(table.isPrimary), // For finding primary images
		index("cat_images_cat_primary_idx").on(table.catId, table.isPrimary), // Critical composite for primary image lookups
		index("cat_images_created_at_idx").on(table.createdAt), // For sorting images by upload time
	]
);

// Clinic profiles table
export const clinicProfiles = pgTable(
	"clinic_profiles",
	{
		id: serial("id").primaryKey(),
		userId: integer("user_id")
			.notNull()
			.references(() => users.id)
			.unique(),
		name: text("name").notNull(),
		slug: text("slug").notNull().unique(),
		address: text("address").notNull(),
		city: text("city").notNull(),
		state: text("state").notNull(),
		zip: text("zip").notNull(),
		phone: text("phone").notNull(),
		website: text("website"),
		description: text("description"),
		verificationStatus: verificationStatusEnum("verification_status")
			.notNull()
			.default("pending"),
		operatingHours: jsonb("operating_hours"),
		emergencyHours: jsonb("emergency_hours"),
		coordinates: point("coordinates"),
		serviceAreaRadius: integer("service_area_radius"),
		amenities: text("amenities").array(),
		specializations: text("specializations").array(),
		pricingInfo: jsonb("pricing_info"),
		socialMedia: jsonb("social_media"),
		images: text("images").array(),
		ratingAverage: decimal("rating_average", { precision: 3, scale: 2 }),
		reviewCount: integer("review_count").default(0).notNull(),
		isActive: boolean("is_active").default(true).notNull(),
		featured: boolean("featured").default(false).notNull(),
		services: text("services"), // Keep for backward compatibility
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => [
		// Slug index for fast lookups
		index("clinic_profiles_slug_idx").on(table.slug),
		// Verification status index for admin queries
		index("clinic_profiles_verification_status_idx").on(
			table.verificationStatus
		),
		// Location-based indexes
		index("clinic_profiles_city_idx").on(table.city),
		index("clinic_profiles_state_idx").on(table.state),
		// Active and featured indexes for public queries
		index("clinic_profiles_is_active_idx").on(table.isActive),
		index("clinic_profiles_featured_idx").on(table.featured),
		// Rating index for sorting
		index("clinic_profiles_rating_idx").on(table.ratingAverage),
	]
);

// Clinic services table
export const clinicServices = pgTable(
	"clinic_services",
	{
		id: serial("id").primaryKey(),
		clinicId: integer("clinic_id")
			.notNull()
			.references(() => clinicProfiles.id, { onDelete: "cascade" }),
		name: text("name").notNull(),
		description: text("description"),
		price: text("price"),
		duration: integer("duration"), // in minutes
		isAvailable: boolean("is_available").default(true).notNull(),
		requiresAppointment: boolean("requires_appointment")
			.default(false)
			.notNull(),
		category: text("category"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => [
		// Clinic index for fast lookups
		index("clinic_services_clinic_id_idx").on(table.clinicId),
		// Availability index for public queries
		index("clinic_services_is_available_idx").on(table.isAvailable),
		// Category index for filtering
		index("clinic_services_category_idx").on(table.category),
	]
);

// Clinic reviews table
export const clinicReviews = pgTable(
	"clinic_reviews",
	{
		id: serial("id").primaryKey(),
		clinicId: integer("clinic_id")
			.notNull()
			.references(() => clinicProfiles.id, { onDelete: "cascade" }),
		userId: integer("user_id")
			.notNull()
			.references(() => users.id, { onDelete: "cascade" }),
		rating: integer("rating").notNull(), // 1-5 stars
		title: text("title"),
		comment: text("comment"),
		isVerified: boolean("is_verified").default(false).notNull(),
		isVisible: boolean("is_visible").default(true).notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => [
		// Clinic index for fast lookups
		index("clinic_reviews_clinic_id_idx").on(table.clinicId),
		// User index to prevent duplicate reviews
		index("clinic_reviews_user_id_idx").on(table.userId),
		// Rating index for sorting
		index("clinic_reviews_rating_idx").on(table.rating),
		// Visibility index for public queries
		index("clinic_reviews_is_visible_idx").on(table.isVisible),
		// Created at index for sorting by date
		index("clinic_reviews_created_at_idx").on(table.createdAt),
	]
);

// Chats table
export const chats = pgTable(
	"chats",
	{
		id: serial("id").primaryKey(),
		catId: integer("cat_id").references(() => cats.id),
		createdAt: timestamp("created_at").defaultNow().notNull(),
	},
	(table) => [
		// Performance indexes for chat queries
		index("chats_cat_id_idx").on(table.catId), // For finding chats by cat
		index("chats_created_at_idx").on(table.createdAt), // For sorting chats by creation time
	]
);

// Chat participants table
export const chatParticipants = pgTable(
	"chat_participants",
	{
		id: serial("id").primaryKey(),
		chatId: integer("chat_id")
			.notNull()
			.references(() => chats.id),
		userId: integer("user_id")
			.notNull()
			.references(() => users.id),
		createdAt: timestamp("created_at").defaultNow().notNull(),
	},
	(table) => [
		// Critical performance indexes for chat participant queries
		index("chat_participants_user_id_idx").on(table.userId), // Primary index for getMyChats query
		index("chat_participants_chat_id_idx").on(table.chatId), // For finding participants by chat
		index("chat_participants_user_chat_idx").on(table.userId, table.chatId), // Composite for unique lookups
		index("chat_participants_created_at_idx").on(table.createdAt), // For sorting by join time
	]
);

// Messages table
export const messages = pgTable(
	"messages",
	{
		id: serial("id").primaryKey(),
		chatId: integer("chat_id")
			.notNull()
			.references(() => chats.id),
		userId: integer("user_id")
			.notNull()
			.references(() => users.id),
		content: text("content").notNull(),
		status: messageStatusEnum("status").notNull().default("sent"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
	},
	(table) => [
		// Performance indexes for message queries
		index("messages_chat_id_idx").on(table.chatId), // For finding messages by chat
		index("messages_user_id_idx").on(table.userId), // For finding messages by user
		index("messages_created_at_idx").on(table.createdAt), // For sorting messages by time
		index("messages_status_idx").on(table.status), // For filtering by read status
		// Composite indexes for common query patterns
		index("messages_chat_created_idx").on(table.chatId, table.createdAt), // Critical for last message queries
		index("messages_chat_user_idx").on(table.chatId, table.userId), // For user-specific message queries
	]
);

// Favorites table
export const favorites = pgTable("favorites", {
	id: serial("id").primaryKey(),
	userId: integer("user_id")
		.notNull()
		.references(() => users.id),
	catId: integer("cat_id")
		.notNull()
		.references(() => cats.id),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Relations
export const usersRelations = relations(users, ({ many, one }) => ({
	cats: many(cats),
	clinicProfile: one(clinicProfiles, {
		fields: [users.id],
		references: [clinicProfiles.userId],
	}),
	clinicReviews: many(clinicReviews),
	wilaya: one(wilayas, {
		fields: [users.wilayaId],
		references: [wilayas.id],
	}),
	commune: one(communes, {
		fields: [users.communeId],
		references: [communes.id],
	}),
	chatParticipants: many(chatParticipants),
	messages: many(messages),
	favorites: many(favorites),
}));

export const catsRelations = relations(cats, ({ one, many }) => ({
	user: one(users, {
		fields: [cats.userId],
		references: [users.id],
	}),
	breed: one(catBreeds, {
		fields: [cats.breedId],
		references: [catBreeds.id],
	}),
	wilaya: one(wilayas, {
		fields: [cats.wilayaId],
		references: [wilayas.id],
	}),
	commune: one(communes, {
		fields: [cats.communeId],
		references: [communes.id],
	}),
	images: many(catImages),
	chats: many(chats),
	favorites: many(favorites),
}));

export const favoritesRelations = relations(favorites, ({ one }) => ({
	user: one(users, {
		fields: [favorites.userId],
		references: [users.id],
	}),
	cat: one(cats, {
		fields: [favorites.catId],
		references: [cats.id],
	}),
}));

export const catBreedsRelations = relations(catBreeds, ({ many }) => ({
	cats: many(cats),
}));

export const wilayasRelations = relations(wilayas, ({ many }) => ({
	communes: many(communes),
	cats: many(cats),
}));

export const communesRelations = relations(communes, ({ one, many }) => ({
	wilaya: one(wilayas, {
		fields: [communes.wilayaId],
		references: [wilayas.id],
	}),
	cats: many(cats),
}));

export const catImagesRelations = relations(catImages, ({ one }) => ({
	cat: one(cats, {
		fields: [catImages.catId],
		references: [cats.id],
	}),
}));

export const clinicProfilesRelations = relations(
	clinicProfiles,
	({ one, many }) => ({
		user: one(users, {
			fields: [clinicProfiles.userId],
			references: [users.id],
		}),
		services: many(clinicServices),
		reviews: many(clinicReviews),
	})
);

export const clinicServicesRelations = relations(
	clinicServices,
	({ one, many }) => ({
		clinic: one(clinicProfiles, {
			fields: [clinicServices.clinicId],
			references: [clinicProfiles.id],
		}),
	})
);

export const clinicReviewsRelations = relations(clinicReviews, ({ one }) => ({
	clinic: one(clinicProfiles, {
		fields: [clinicReviews.clinicId],
		references: [clinicProfiles.id],
	}),
	user: one(users, {
		fields: [clinicReviews.userId],
		references: [users.id],
	}),
}));

export const chatsRelations = relations(chats, ({ one, many }) => ({
	cat: one(cats, {
		fields: [chats.catId],
		references: [cats.id],
	}),
	participants: many(chatParticipants),
	messages: many(messages),
}));

export const chatParticipantsRelations = relations(
	chatParticipants,
	({ one }) => ({
		chat: one(chats, {
			fields: [chatParticipants.chatId],
			references: [chats.id],
		}),
		user: one(users, {
			fields: [chatParticipants.userId],
			references: [users.id],
		}),
	})
);

export const messagesRelations = relations(messages, ({ one }) => ({
	chat: one(chats, {
		fields: [messages.chatId],
		references: [chats.id],
	}),
	user: one(users, {
		fields: [messages.userId],
		references: [users.id],
	}),
}));
