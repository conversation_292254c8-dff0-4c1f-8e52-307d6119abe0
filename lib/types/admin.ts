export interface AdminStats {
	totalUsers: number;
	totalCats: number;
	totalMessages: number;
	totalFavorites: number;
	usersByRole: Record<string, number>;
	catsByStatus: Record<string, number>;
	recentUsers: number;
	recentCats: number;
}

export interface MonthlyAdoptionData {
	month: string;
	count: number;
	year: number;
	monthNum: number;
}

export interface WeeklyAdoptionData {
	week: string;
	count: number;
	weekId: string;
}

export interface AdoptionStatsResponse {
	period: "monthly" | "weekly";
	data: MonthlyAdoptionData[] | WeeklyAdoptionData[];
}

export interface ChartDataPoint {
	name: string;
	value: number;
}

export interface AdoptionChartData {
	month?: string;
	week?: string;
	count: number;
}

export interface CatStatusChartData {
	name: string;
	value: number;
	status: string;
}
