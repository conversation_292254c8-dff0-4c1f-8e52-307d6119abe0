{"id": "1b7a6ad9-1edc-4516-ac34-50f3c82ea249", "prevId": "de1d016b-70dc-4070-848d-74f3138f5d11", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cat_breeds": {"name": "cat_breeds", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "origin": {"name": "origin", "type": "text", "primaryKey": false, "notNull": false}, "temperament": {"name": "temperament", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"cat_breeds_name_idx": {"name": "cat_breeds_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"cat_breeds_name_unique": {"name": "cat_breeds_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cat_images": {"name": "cat_images", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "cat_id": {"name": "cat_id", "type": "integer", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"cat_images_cat_id_idx": {"name": "cat_images_cat_id_idx", "columns": [{"expression": "cat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cat_images_is_primary_idx": {"name": "cat_images_is_primary_idx", "columns": [{"expression": "is_primary", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cat_images_cat_primary_idx": {"name": "cat_images_cat_primary_idx", "columns": [{"expression": "cat_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_primary", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cat_images_created_at_idx": {"name": "cat_images_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"cat_images_cat_id_cats_id_fk": {"name": "cat_images_cat_id_cats_id_fk", "tableFrom": "cat_images", "tableTo": "cats", "columnsFrom": ["cat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.cats": {"name": "cats", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "gender": {"name": "gender", "type": "cat_gender", "typeSchema": "public", "primaryKey": false, "notNull": true}, "age": {"name": "age", "type": "integer", "primaryKey": false, "notNull": true}, "breed_id": {"name": "breed_id", "type": "integer", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "story": {"name": "story", "type": "text", "primaryKey": false, "notNull": false}, "wilaya_id": {"name": "wilaya_id", "type": "integer", "primaryKey": false, "notNull": false}, "commune_id": {"name": "commune_id", "type": "integer", "primaryKey": false, "notNull": false}, "vaccinated": {"name": "vaccinated", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "neutered": {"name": "neutered", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "special_needs": {"name": "special_needs", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "special_needs_description": {"name": "special_needs_description", "type": "text", "primaryKey": false, "notNull": false}, "adopted": {"name": "adopted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "status": {"name": "status", "type": "cat_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'available'"}, "is_draft": {"name": "is_draft", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "featured": {"name": "featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"cats_name_idx": {"name": "cats_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_description_idx": {"name": "cats_description_idx", "columns": [{"expression": "description", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_story_idx": {"name": "cats_story_idx", "columns": [{"expression": "story", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_slug_idx": {"name": "cats_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_special_needs_desc_idx": {"name": "cats_special_needs_desc_idx", "columns": [{"expression": "special_needs_description", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_breed_id_idx": {"name": "cats_breed_id_idx", "columns": [{"expression": "breed_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_wilaya_id_idx": {"name": "cats_wilaya_id_idx", "columns": [{"expression": "wilaya_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_commune_id_idx": {"name": "cats_commune_id_idx", "columns": [{"expression": "commune_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_user_id_idx": {"name": "cats_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_is_draft_idx": {"name": "cats_is_draft_idx", "columns": [{"expression": "is_draft", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_adopted_idx": {"name": "cats_adopted_idx", "columns": [{"expression": "adopted", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_created_at_idx": {"name": "cats_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_status_idx": {"name": "cats_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_featured_idx": {"name": "cats_featured_idx", "columns": [{"expression": "featured", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_gender_idx": {"name": "cats_gender_idx", "columns": [{"expression": "gender", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_vaccinated_idx": {"name": "cats_vaccinated_idx", "columns": [{"expression": "vaccinated", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_neutered_idx": {"name": "cats_neutered_idx", "columns": [{"expression": "neutered", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_special_needs_idx": {"name": "cats_special_needs_idx", "columns": [{"expression": "special_needs", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_is_draft_adopted_idx": {"name": "cats_is_draft_adopted_idx", "columns": [{"expression": "is_draft", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "adopted", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_is_draft_created_at_idx": {"name": "cats_is_draft_created_at_idx", "columns": [{"expression": "is_draft", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_is_draft_status_idx": {"name": "cats_is_draft_status_idx", "columns": [{"expression": "is_draft", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_is_draft_featured_idx": {"name": "cats_is_draft_featured_idx", "columns": [{"expression": "is_draft", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "featured", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_age_idx": {"name": "cats_age_idx", "columns": [{"expression": "age", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cats_is_draft_age_idx": {"name": "cats_is_draft_age_idx", "columns": [{"expression": "is_draft", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "age", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"cats_breed_id_cat_breeds_id_fk": {"name": "cats_breed_id_cat_breeds_id_fk", "tableFrom": "cats", "tableTo": "cat_breeds", "columnsFrom": ["breed_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "cats_wilaya_id_wilayas_id_fk": {"name": "cats_wilaya_id_wilayas_id_fk", "tableFrom": "cats", "tableTo": "wilayas", "columnsFrom": ["wilaya_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "cats_commune_id_communes_id_fk": {"name": "cats_commune_id_communes_id_fk", "tableFrom": "cats", "tableTo": "communes", "columnsFrom": ["commune_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "cats_user_id_users_id_fk": {"name": "cats_user_id_users_id_fk", "tableFrom": "cats", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"cats_slug_unique": {"name": "cats_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_participants": {"name": "chat_participants", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "chat_id": {"name": "chat_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"chat_participants_user_id_idx": {"name": "chat_participants_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_participants_chat_id_idx": {"name": "chat_participants_chat_id_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_participants_user_chat_idx": {"name": "chat_participants_user_chat_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_participants_created_at_idx": {"name": "chat_participants_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_participants_chat_id_chats_id_fk": {"name": "chat_participants_chat_id_chats_id_fk", "tableFrom": "chat_participants", "tableTo": "chats", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_participants_user_id_users_id_fk": {"name": "chat_participants_user_id_users_id_fk", "tableFrom": "chat_participants", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chats": {"name": "chats", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "cat_id": {"name": "cat_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"chats_cat_id_idx": {"name": "chats_cat_id_idx", "columns": [{"expression": "cat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chats_created_at_idx": {"name": "chats_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chats_cat_id_cats_id_fk": {"name": "chats_cat_id_cats_id_fk", "tableFrom": "chats", "tableTo": "cats", "columnsFrom": ["cat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.clinic_appointments": {"name": "clinic_appointments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "clinic_id": {"name": "clinic_id", "type": "integer", "primaryKey": false, "notNull": true}, "service_id": {"name": "service_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "cat_id": {"name": "cat_id", "type": "integer", "primaryKey": false, "notNull": false}, "appointment_date": {"name": "appointment_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"clinic_appointments_clinic_id_idx": {"name": "clinic_appointments_clinic_id_idx", "columns": [{"expression": "clinic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_appointments_user_id_idx": {"name": "clinic_appointments_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_appointments_appointment_date_idx": {"name": "clinic_appointments_appointment_date_idx", "columns": [{"expression": "appointment_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_appointments_status_idx": {"name": "clinic_appointments_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"clinic_appointments_clinic_id_clinic_profiles_id_fk": {"name": "clinic_appointments_clinic_id_clinic_profiles_id_fk", "tableFrom": "clinic_appointments", "tableTo": "clinic_profiles", "columnsFrom": ["clinic_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "clinic_appointments_service_id_clinic_services_id_fk": {"name": "clinic_appointments_service_id_clinic_services_id_fk", "tableFrom": "clinic_appointments", "tableTo": "clinic_services", "columnsFrom": ["service_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "clinic_appointments_user_id_users_id_fk": {"name": "clinic_appointments_user_id_users_id_fk", "tableFrom": "clinic_appointments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "clinic_appointments_cat_id_cats_id_fk": {"name": "clinic_appointments_cat_id_cats_id_fk", "tableFrom": "clinic_appointments", "tableTo": "cats", "columnsFrom": ["cat_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.clinic_profiles": {"name": "clinic_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true}, "zip": {"name": "zip", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "verification_status": {"name": "verification_status", "type": "verification_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "operating_hours": {"name": "operating_hours", "type": "jsonb", "primaryKey": false, "notNull": false}, "emergency_hours": {"name": "emergency_hours", "type": "jsonb", "primaryKey": false, "notNull": false}, "coordinates": {"name": "coordinates", "type": "point", "primaryKey": false, "notNull": false}, "service_area_radius": {"name": "service_area_radius", "type": "integer", "primaryKey": false, "notNull": false}, "amenities": {"name": "amenities", "type": "text[]", "primaryKey": false, "notNull": false}, "specializations": {"name": "specializations", "type": "text[]", "primaryKey": false, "notNull": false}, "pricing_info": {"name": "pricing_info", "type": "jsonb", "primaryKey": false, "notNull": false}, "social_media": {"name": "social_media", "type": "jsonb", "primaryKey": false, "notNull": false}, "images": {"name": "images", "type": "text[]", "primaryKey": false, "notNull": false}, "rating_average": {"name": "rating_average", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false}, "review_count": {"name": "review_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "featured": {"name": "featured", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "services": {"name": "services", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"clinic_profiles_slug_idx": {"name": "clinic_profiles_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_profiles_verification_status_idx": {"name": "clinic_profiles_verification_status_idx", "columns": [{"expression": "verification_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_profiles_city_idx": {"name": "clinic_profiles_city_idx", "columns": [{"expression": "city", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_profiles_state_idx": {"name": "clinic_profiles_state_idx", "columns": [{"expression": "state", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_profiles_is_active_idx": {"name": "clinic_profiles_is_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_profiles_featured_idx": {"name": "clinic_profiles_featured_idx", "columns": [{"expression": "featured", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_profiles_rating_idx": {"name": "clinic_profiles_rating_idx", "columns": [{"expression": "rating_average", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"clinic_profiles_user_id_users_id_fk": {"name": "clinic_profiles_user_id_users_id_fk", "tableFrom": "clinic_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"clinic_profiles_user_id_unique": {"name": "clinic_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}, "clinic_profiles_slug_unique": {"name": "clinic_profiles_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.clinic_reviews": {"name": "clinic_reviews", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "clinic_id": {"name": "clinic_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_visible": {"name": "is_visible", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"clinic_reviews_clinic_id_idx": {"name": "clinic_reviews_clinic_id_idx", "columns": [{"expression": "clinic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_reviews_user_id_idx": {"name": "clinic_reviews_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_reviews_rating_idx": {"name": "clinic_reviews_rating_idx", "columns": [{"expression": "rating", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_reviews_is_visible_idx": {"name": "clinic_reviews_is_visible_idx", "columns": [{"expression": "is_visible", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_reviews_created_at_idx": {"name": "clinic_reviews_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"clinic_reviews_clinic_id_clinic_profiles_id_fk": {"name": "clinic_reviews_clinic_id_clinic_profiles_id_fk", "tableFrom": "clinic_reviews", "tableTo": "clinic_profiles", "columnsFrom": ["clinic_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "clinic_reviews_user_id_users_id_fk": {"name": "clinic_reviews_user_id_users_id_fk", "tableFrom": "clinic_reviews", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.clinic_services": {"name": "clinic_services", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "clinic_id": {"name": "clinic_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "is_available": {"name": "is_available", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "requires_appointment": {"name": "requires_appointment", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"clinic_services_clinic_id_idx": {"name": "clinic_services_clinic_id_idx", "columns": [{"expression": "clinic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_services_is_available_idx": {"name": "clinic_services_is_available_idx", "columns": [{"expression": "is_available", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_services_category_idx": {"name": "clinic_services_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"clinic_services_clinic_id_clinic_profiles_id_fk": {"name": "clinic_services_clinic_id_clinic_profiles_id_fk", "tableFrom": "clinic_services", "tableTo": "clinic_profiles", "columnsFrom": ["clinic_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.clinic_verification_documents": {"name": "clinic_verification_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "clinic_id": {"name": "clinic_id", "type": "integer", "primaryKey": false, "notNull": true}, "document_type": {"name": "document_type", "type": "text", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "verified_by": {"name": "verified_by", "type": "integer", "primaryKey": false, "notNull": false}, "verified_at": {"name": "verified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"clinic_verification_documents_clinic_id_idx": {"name": "clinic_verification_documents_clinic_id_idx", "columns": [{"expression": "clinic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_verification_documents_document_type_idx": {"name": "clinic_verification_documents_document_type_idx", "columns": [{"expression": "document_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "clinic_verification_documents_is_verified_idx": {"name": "clinic_verification_documents_is_verified_idx", "columns": [{"expression": "is_verified", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"clinic_verification_documents_clinic_id_clinic_profiles_id_fk": {"name": "clinic_verification_documents_clinic_id_clinic_profiles_id_fk", "tableFrom": "clinic_verification_documents", "tableTo": "clinic_profiles", "columnsFrom": ["clinic_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "clinic_verification_documents_verified_by_users_id_fk": {"name": "clinic_verification_documents_verified_by_users_id_fk", "tableFrom": "clinic_verification_documents", "tableTo": "users", "columnsFrom": ["verified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.communes": {"name": "communes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "name_ar": {"name": "name_ar", "type": "text", "primaryKey": false, "notNull": false}, "name_fr": {"name": "name_fr", "type": "text", "primaryKey": false, "notNull": false}, "wilaya_id": {"name": "wilaya_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"communes_name_idx": {"name": "communes_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "communes_name_ar_idx": {"name": "communes_name_ar_idx", "columns": [{"expression": "name_ar", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "communes_name_fr_idx": {"name": "communes_name_fr_idx", "columns": [{"expression": "name_fr", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "communes_wilaya_id_idx": {"name": "communes_wilaya_id_idx", "columns": [{"expression": "wilaya_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"communes_wilaya_id_wilayas_id_fk": {"name": "communes_wilaya_id_wilayas_id_fk", "tableFrom": "communes", "tableTo": "wilayas", "columnsFrom": ["wilaya_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.favorites": {"name": "favorites", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "cat_id": {"name": "cat_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"favorites_user_id_users_id_fk": {"name": "favorites_user_id_users_id_fk", "tableFrom": "favorites", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "favorites_cat_id_cats_id_fk": {"name": "favorites_cat_id_cats_id_fk", "tableFrom": "favorites", "tableTo": "cats", "columnsFrom": ["cat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "chat_id": {"name": "chat_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "message_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'sent'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"messages_chat_id_idx": {"name": "messages_chat_id_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_user_id_idx": {"name": "messages_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_created_at_idx": {"name": "messages_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_status_idx": {"name": "messages_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_chat_created_idx": {"name": "messages_chat_created_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_chat_user_idx": {"name": "messages_chat_user_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"messages_chat_id_chats_id_fk": {"name": "messages_chat_id_chats_id_fk", "tableFrom": "messages", "tableTo": "chats", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "messages_user_id_users_id_fk": {"name": "messages_user_id_users_id_fk", "tableFrom": "messages", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_token_unique": {"name": "sessions_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'adopter'"}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "wilaya_id": {"name": "wilaya_id", "type": "integer", "primaryKey": false, "notNull": false}, "commune_id": {"name": "commune_id", "type": "integer", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"users_name_idx": {"name": "users_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_slug_idx": {"name": "users_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_wilaya_id_idx": {"name": "users_wilaya_id_idx", "columns": [{"expression": "wilaya_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_commune_id_idx": {"name": "users_commune_id_idx", "columns": [{"expression": "commune_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_wilaya_id_wilayas_id_fk": {"name": "users_wilaya_id_wilayas_id_fk", "tableFrom": "users", "tableTo": "wilayas", "columnsFrom": ["wilaya_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "users_commune_id_communes_id_fk": {"name": "users_commune_id_communes_id_fk", "tableFrom": "users", "tableTo": "communes", "columnsFrom": ["commune_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_slug_unique": {"name": "users_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verifications": {"name": "verifications", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.wilayas": {"name": "wilayas", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "name_ar": {"name": "name_ar", "type": "text", "primaryKey": false, "notNull": false}, "name_fr": {"name": "name_fr", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"wilayas_name_idx": {"name": "wilayas_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "wilayas_name_ar_idx": {"name": "wilayas_name_ar_idx", "columns": [{"expression": "name_ar", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "wilayas_name_fr_idx": {"name": "wilayas_name_fr_idx", "columns": [{"expression": "name_fr", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"wilayas_code_unique": {"name": "wilayas_code_unique", "nullsNotDistinct": false, "columns": ["code"]}, "wilayas_name_unique": {"name": "wilayas_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.adoption_status": {"name": "adoption_status", "schema": "public", "values": ["pending", "approved", "rejected"]}, "public.cat_gender": {"name": "cat_gender", "schema": "public", "values": ["male", "female"]}, "public.cat_status": {"name": "cat_status", "schema": "public", "values": ["available", "pending", "adopted", "unavailable"]}, "public.message_status": {"name": "message_status", "schema": "public", "values": ["sent", "delivered", "read"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["admin", "adopter", "rescuer", "clinic"]}, "public.verification_status": {"name": "verification_status", "schema": "public", "values": ["pending", "verified", "rejected"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}