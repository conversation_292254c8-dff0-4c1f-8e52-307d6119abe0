CREATE TYPE "public"."verification_status" AS ENUM('pending', 'verified', 'rejected');--> statement-breakpoint
CREATE TABLE "clinic_appointments" (
	"id" serial PRIMARY KEY NOT NULL,
	"clinic_id" integer NOT NULL,
	"service_id" integer,
	"user_id" integer NOT NULL,
	"cat_id" integer,
	"appointment_date" timestamp NOT NULL,
	"duration" integer DEFAULT 30 NOT NULL,
	"status" text DEFAULT 'scheduled' NOT NULL,
	"notes" text,
	"price" numeric(10, 2),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "clinic_reviews" (
	"id" serial PRIMARY KEY NOT NULL,
	"clinic_id" integer NOT NULL,
	"user_id" integer NOT NULL,
	"rating" integer NOT NULL,
	"title" text,
	"comment" text,
	"is_verified" boolean DEFAULT false NOT NULL,
	"is_visible" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "clinic_services" (
	"id" serial PRIMARY KEY NOT NULL,
	"clinic_id" integer NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"price" text,
	"duration" integer,
	"is_available" boolean DEFAULT true NOT NULL,
	"requires_appointment" boolean DEFAULT false NOT NULL,
	"category" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "clinic_verification_documents" (
	"id" serial PRIMARY KEY NOT NULL,
	"clinic_id" integer NOT NULL,
	"document_type" text NOT NULL,
	"file_name" text NOT NULL,
	"file_url" text NOT NULL,
	"file_size" integer,
	"mime_type" text,
	"is_verified" boolean DEFAULT false NOT NULL,
	"verified_by" integer,
	"verified_at" timestamp,
	"notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "slug" text NOT NULL;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "description" text;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "verification_status" "verification_status" DEFAULT 'pending' NOT NULL;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "operating_hours" jsonb;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "emergency_hours" jsonb;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "coordinates" "point";--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "service_area_radius" integer;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "amenities" text[];--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "specializations" text[];--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "pricing_info" jsonb;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "social_media" jsonb;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "images" text[];--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "rating_average" numeric(3, 2);--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "review_count" integer DEFAULT 0 NOT NULL;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "is_active" boolean DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD COLUMN "featured" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "clinic_appointments" ADD CONSTRAINT "clinic_appointments_clinic_id_clinic_profiles_id_fk" FOREIGN KEY ("clinic_id") REFERENCES "public"."clinic_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clinic_appointments" ADD CONSTRAINT "clinic_appointments_service_id_clinic_services_id_fk" FOREIGN KEY ("service_id") REFERENCES "public"."clinic_services"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clinic_appointments" ADD CONSTRAINT "clinic_appointments_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clinic_appointments" ADD CONSTRAINT "clinic_appointments_cat_id_cats_id_fk" FOREIGN KEY ("cat_id") REFERENCES "public"."cats"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clinic_reviews" ADD CONSTRAINT "clinic_reviews_clinic_id_clinic_profiles_id_fk" FOREIGN KEY ("clinic_id") REFERENCES "public"."clinic_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clinic_reviews" ADD CONSTRAINT "clinic_reviews_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clinic_services" ADD CONSTRAINT "clinic_services_clinic_id_clinic_profiles_id_fk" FOREIGN KEY ("clinic_id") REFERENCES "public"."clinic_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clinic_verification_documents" ADD CONSTRAINT "clinic_verification_documents_clinic_id_clinic_profiles_id_fk" FOREIGN KEY ("clinic_id") REFERENCES "public"."clinic_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "clinic_verification_documents" ADD CONSTRAINT "clinic_verification_documents_verified_by_users_id_fk" FOREIGN KEY ("verified_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "clinic_appointments_clinic_id_idx" ON "clinic_appointments" USING btree ("clinic_id");--> statement-breakpoint
CREATE INDEX "clinic_appointments_user_id_idx" ON "clinic_appointments" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "clinic_appointments_appointment_date_idx" ON "clinic_appointments" USING btree ("appointment_date");--> statement-breakpoint
CREATE INDEX "clinic_appointments_status_idx" ON "clinic_appointments" USING btree ("status");--> statement-breakpoint
CREATE INDEX "clinic_reviews_clinic_id_idx" ON "clinic_reviews" USING btree ("clinic_id");--> statement-breakpoint
CREATE INDEX "clinic_reviews_user_id_idx" ON "clinic_reviews" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "clinic_reviews_rating_idx" ON "clinic_reviews" USING btree ("rating");--> statement-breakpoint
CREATE INDEX "clinic_reviews_is_visible_idx" ON "clinic_reviews" USING btree ("is_visible");--> statement-breakpoint
CREATE INDEX "clinic_reviews_created_at_idx" ON "clinic_reviews" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "clinic_services_clinic_id_idx" ON "clinic_services" USING btree ("clinic_id");--> statement-breakpoint
CREATE INDEX "clinic_services_is_available_idx" ON "clinic_services" USING btree ("is_available");--> statement-breakpoint
CREATE INDEX "clinic_services_category_idx" ON "clinic_services" USING btree ("category");--> statement-breakpoint
CREATE INDEX "clinic_verification_documents_clinic_id_idx" ON "clinic_verification_documents" USING btree ("clinic_id");--> statement-breakpoint
CREATE INDEX "clinic_verification_documents_document_type_idx" ON "clinic_verification_documents" USING btree ("document_type");--> statement-breakpoint
CREATE INDEX "clinic_verification_documents_is_verified_idx" ON "clinic_verification_documents" USING btree ("is_verified");--> statement-breakpoint
CREATE INDEX "clinic_profiles_slug_idx" ON "clinic_profiles" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "clinic_profiles_verification_status_idx" ON "clinic_profiles" USING btree ("verification_status");--> statement-breakpoint
CREATE INDEX "clinic_profiles_city_idx" ON "clinic_profiles" USING btree ("city");--> statement-breakpoint
CREATE INDEX "clinic_profiles_state_idx" ON "clinic_profiles" USING btree ("state");--> statement-breakpoint
CREATE INDEX "clinic_profiles_is_active_idx" ON "clinic_profiles" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "clinic_profiles_featured_idx" ON "clinic_profiles" USING btree ("featured");--> statement-breakpoint
CREATE INDEX "clinic_profiles_rating_idx" ON "clinic_profiles" USING btree ("rating_average");--> statement-breakpoint
ALTER TABLE "clinic_profiles" ADD CONSTRAINT "clinic_profiles_slug_unique" UNIQUE("slug");