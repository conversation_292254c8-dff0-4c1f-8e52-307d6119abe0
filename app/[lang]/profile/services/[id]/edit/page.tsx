import { notFound, redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { api } from "@/lib/trpc/server";
import { ClinicServicesForm } from "@/components/profile/clinic-services-form";

interface EditServicePageProps {
	params: Promise<{
		lang: string;
		id: string;
	}>;
}

export default async function EditServicePage({ params }: EditServicePageProps) {
	const resolvedParams = await params;
	const t = await getTranslations("profile");

	// Fetch the service
	let service = null;
	try {
		service = await api.clinicServices.get({ id: resolvedParams.id });
	} catch (error) {
		notFound();
	}

	// Fetch clinic profile
	let clinicProfile = null;
	try {
		clinicProfile = await api.clinics.getMy();
	} catch (error) {
		// Clinic profile doesn't exist yet
		redirect(`/${resolvedParams.lang}/profile/clinic/profile`);
	}

	// Check if service belongs to this clinic
	if (service.clinicId !== clinicProfile.id) {
		notFound();
	}

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-2xl font-bold text-gray-900">
					{t("editService")}
				</h1>
				<p className="text-gray-600 mt-1">
					{t("editServiceDescription")}
				</p>
			</div>
			<ClinicServicesForm
				service={service}
				clinicProfile={clinicProfile}
				onSuccess={() =>
					redirect(`/${resolvedParams.lang}/profile/services`)
				}
			/>
		</div>
	);
}

export async function generateMetadata({ params }: EditServicePageProps) {
	const resolvedParams = await params;
	const t = await getTranslations("profile");

	return {
		title: t("editService"),
		description: t("editServiceDescription"),
	};
}
