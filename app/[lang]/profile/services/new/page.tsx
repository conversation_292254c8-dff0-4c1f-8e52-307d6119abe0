import { ClinicServicesForm } from "@/components/profile/clinic-services-form";
import { redirect } from "@/lib/i18n/navigation";
import { api } from "@/lib/trpc/server";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";

interface AddServicePageProps {
	params: Promise<{
		lang: Locale;
	}>;
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const resolvedParams = await params;
	const t = await getTranslations({
		locale: resolvedParams.lang,
		namespace: "profile",
	});

	return {
		title: `${t("addNewService")} - ${t("title")}`,
		description: t("addNewServiceDescription"),
	};
}

export default async function AddServicePage({ params }: AddServicePageProps) {
	const resolvedParams = await params;

	// Enable static rendering
	setRequestLocale(resolvedParams.lang);

	// Fetch user profile using tRPC (authentication is handled by layout)
	const user = await api.users.getProfile();
	const t = await getTranslations("profile");

	// Check if user has permission to access this page
	// Allow admins to access clinic pages for testing purposes
	if (user.role !== "clinic" && user.role !== "admin") {
		redirect({
			href: "/profile/favorites",
			locale: resolvedParams.lang,
		});
	}

	// Fetch clinic profile
	let clinicProfile = null;
	try {
		clinicProfile = await api.clinics.getMy();
	} catch (error) {
		// Clinic profile doesn't exist yet
		redirect({
			href: "/profile/clinic/profile",
			locale: resolvedParams.lang,
		});
	}

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-2xl font-bold text-gray-900">
					{t("addNewService")}
				</h1>
				<p className="text-gray-600 mt-1">
					{t("addNewServiceDescription")}
				</p>
			</div>
			<ClinicServicesForm
				clinicProfile={clinicProfile}
				onSuccess={() =>
					redirect({
						href: "/profile/services",
						locale: resolvedParams.lang,
					})
				}
			/>
		</div>
	);
}
