import { api } from "@/lib/trpc/server";
import { setRequestLocale } from "next-intl/server";
import { getTranslations } from "next-intl/server";
import { redirect } from "@/lib/i18n/navigation";
import type { Locale } from "@/lib/i18n/routing";
import { ClinicOverview } from "@/components/profile/clinic/clinic-overview";

interface ClinicProfilePageProps {
	params: Promise<{
		lang: Locale;
	}>;
}

export default async function ClinicProfilePage({
	params,
}: ClinicProfilePageProps) {
	const resolvedParams = await params;

	// Enable static rendering
	setRequestLocale(resolvedParams.lang);

	// Fetch user profile using tRPC (authentication is handled by layout)
	const user = await api.users.getProfile();
	const t = await getTranslations("profile");

	// Check if user has permission to access this page
	// Allow admins to access clinic pages for testing purposes
	if (user.role !== "clinic" && user.role !== "admin") {
		redirect({
			href: "/profile/favorites",
			locale: resolvedParams.lang,
		});
	}

	// Fetch clinic profile
	let clinicProfile = null;
	try {
		clinicProfile = await api.clinics.getMy();
	} catch (error) {
		// Clinic profile doesn't exist yet
		console.log("No clinic profile found for user");
	}

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-2xl font-bold text-gray-900">
					{t("clinicOverview")}
				</h1>
				<p className="text-gray-600 mt-1">
					{t("clinicOverviewDescription")}
				</p>
			</div>
			<ClinicOverview user={user} clinicProfile={clinicProfile} />
		</div>
	);
}
