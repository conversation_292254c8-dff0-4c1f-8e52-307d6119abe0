import { api } from "@/lib/trpc/server";
import { setRequestLocale } from "next-intl/server";
import { getTranslations } from "next-intl/server";
import { redirect } from "@/lib/i18n/navigation";
import type { Locale } from "@/lib/i18n/routing";
import { ClinicProfileManagement } from "@/components/profile/clinic/clinic-profile-management";

interface ClinicProfileManagementPageProps {
	params: Promise<{
		lang: Locale;
	}>;
}

export default async function ClinicProfileManagementPage({
	params,
}: ClinicProfileManagementPageProps) {
	const resolvedParams = await params;

	// Enable static rendering
	setRequestLocale(resolvedParams.lang);

	// Fetch user profile using tRPC (authentication is handled by layout)
	const user = await api.users.getProfile();
	const t = await getTranslations("profile");

	// Check if user has permission to access this page
	// Allow admins to access clinic pages for testing purposes
	if (user.role !== "clinic" && user.role !== "admin") {
		redirect({
			href: "/profile/favorites",
			locale: resolvedParams.lang,
		});
	}

	// Fetch clinic profile
	let clinicProfile = null;
	try {
		clinicProfile = await api.clinics.getMy();
	} catch (error) {
		// Clinic profile doesn't exist yet
		console.log("No clinic profile found for user");
	}

	return (
		<div className="space-y-6">
			<div>
				<h1 className="text-2xl font-bold text-gray-900">
					{t("clinicProfileManagement")}
				</h1>
				<p className="text-gray-600 mt-1">
					{t("clinicProfileManagementDescription")}
				</p>
			</div>
			<ClinicProfileManagement
				user={user}
				clinicProfile={clinicProfile}
			/>
		</div>
	);
}
