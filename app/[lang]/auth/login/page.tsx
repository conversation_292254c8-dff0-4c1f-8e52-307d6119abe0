import Link from "next/link";
import { Suspense } from "react";
import { LoginForm } from "@/components/login-form";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";

export async function generateMetadata({
	params,
}: {
	params: { lang: Locale };
}) {
	const { lang } = await params;
	const t = await getTranslations({ locale: lang, namespace: "forms.login" });
	const common = await getTranslations({ locale: lang, namespace: "common" });

	return {
		title: `${t("title")} - ${common("appName")}`,
		description: "Sign in to your account",
	};
}

export default async function LoginPage({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const { lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	// Get translations
	const t = await getTranslations({ locale: lang, namespace: "forms.login" });
	const registerT = await getTranslations({
		locale: lang,
		namespace: "forms.register",
	});

	return (
		<div className="flex min-h-[calc(100vh-4rem)] w-full items-center justify-center">
			<main className="container w-full max-w-md px-4 py-16">
				<Card>
					<CardHeader className="space-y-1">
						<CardTitle className="text-2xl">{t("title")}</CardTitle>
						<CardDescription>
							{t("description") ||
								"Enter your email and password to access your account"}
						</CardDescription>
					</CardHeader>
					<CardContent className="pt-4">
						<Suspense fallback={<div>Loading...</div>}>
							<LoginForm />
						</Suspense>
					</CardContent>
					<CardFooter className="flex flex-col items-center justify-center gap-2">
						<div className="text-sm text-muted-foreground">
							{t("noAccount")}{" "}
							<Link
								href={`/${lang}/auth/register`}
								className="text-primary hover:underline"
							>
								{registerT("title")}
							</Link>
						</div>
					</CardFooter>
				</Card>
			</main>
		</div>
	);
}
