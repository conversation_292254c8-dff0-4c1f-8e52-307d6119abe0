import { ClinicRegistrationForm } from "@/components/clinic/clinic-registration-form";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import Link from "next/link";

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const { lang } = await params;
	const clinicT = await getTranslations({
		locale: lang,
		namespace: "clinic.registration",
	});
	const common = await getTranslations({ locale: lang, namespace: "common" });

	return {
		title: `${clinicT("title")} - ${common("appName")}`,
		description: clinicT("description"),
	};
}

export default async function ClinicRegistrationPage({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const { lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	// Get translations
	const clinicT = await getTranslations({
		locale: lang,
		namespace: "clinic.registration",
	});
	const loginT = await getTranslations({
		locale: lang,
		namespace: "forms.login",
	});

	return (
		<div className="min-h-[calc(100vh-4rem)] w-full bg-gray-50">
			<div className="container mx-auto px-4 py-8">
				<div className="mx-auto max-w-4xl">
					{/* Header */}
					<div className="mb-8 text-center">
						<h1 className="text-3xl font-bold text-gray-900">
							{clinicT("title")}
						</h1>
						<p className="mt-2 text-lg text-gray-600">
							{clinicT("subtitle")}
						</p>
					</div>

					{/* Registration Form */}
					<Card className="shadow-lg">
						<CardHeader className="space-y-1">
							<CardTitle className="text-2xl">
								{clinicT("formTitle")}
							</CardTitle>
							<CardDescription>
								{clinicT("formDescription")}
							</CardDescription>
						</CardHeader>
						<CardContent className="pt-4">
							<ClinicRegistrationForm />
						</CardContent>
					</Card>

					{/* Footer */}
					<div className="mt-8 text-center">
						<div className="text-sm text-muted-foreground">
							{clinicT("hasAccount")}{" "}
							<Link
								href={`/${lang}/auth/login`}
								className="text-primary hover:underline"
							>
								{loginT("title")}
							</Link>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
